// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		18168F7D2DF07D8C001D62C9 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 18168F652DF07D8A001D62C9 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 18168F6C2DF07D8A001D62C9;
			remoteInfo = PhotoSwipe;
		};
		18168F872DF07D8C001D62C9 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 18168F652DF07D8A001D62C9 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 18168F6C2DF07D8A001D62C9;
			remoteInfo = PhotoSwipe;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		18168F6D2DF07D8A001D62C9 /* PhotoSwipe.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PhotoSwipe.app; sourceTree = BUILT_PRODUCTS_DIR; };
		18168F7C2DF07D8C001D62C9 /* PhotoSwipeTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PhotoSwipeTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		18168F862DF07D8C001D62C9 /* PhotoSwipeUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PhotoSwipeUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		18168F6F2DF07D8A001D62C9 /* PhotoSwipe */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PhotoSwipe;
			sourceTree = "<group>";
		};
		18168F7F2DF07D8C001D62C9 /* PhotoSwipeTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PhotoSwipeTests;
			sourceTree = "<group>";
		};
		18168F892DF07D8C001D62C9 /* PhotoSwipeUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PhotoSwipeUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		18168F6A2DF07D8A001D62C9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18168F792DF07D8C001D62C9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18168F832DF07D8C001D62C9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		18168F642DF07D8A001D62C9 = {
			isa = PBXGroup;
			children = (
				18168F6F2DF07D8A001D62C9 /* PhotoSwipe */,
				18168F7F2DF07D8C001D62C9 /* PhotoSwipeTests */,
				18168F892DF07D8C001D62C9 /* PhotoSwipeUITests */,
				18168F6E2DF07D8A001D62C9 /* Products */,
			);
			sourceTree = "<group>";
		};
		18168F6E2DF07D8A001D62C9 /* Products */ = {
			isa = PBXGroup;
			children = (
				18168F6D2DF07D8A001D62C9 /* PhotoSwipe.app */,
				18168F7C2DF07D8C001D62C9 /* PhotoSwipeTests.xctest */,
				18168F862DF07D8C001D62C9 /* PhotoSwipeUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		18168F6C2DF07D8A001D62C9 /* PhotoSwipe */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18168F902DF07D8C001D62C9 /* Build configuration list for PBXNativeTarget "PhotoSwipe" */;
			buildPhases = (
				18168F692DF07D8A001D62C9 /* Sources */,
				18168F6A2DF07D8A001D62C9 /* Frameworks */,
				18168F6B2DF07D8A001D62C9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				18168F6F2DF07D8A001D62C9 /* PhotoSwipe */,
			);
			name = PhotoSwipe;
			packageProductDependencies = (
			);
			productName = PhotoSwipe;
			productReference = 18168F6D2DF07D8A001D62C9 /* PhotoSwipe.app */;
			productType = "com.apple.product-type.application";
		};
		18168F7B2DF07D8C001D62C9 /* PhotoSwipeTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18168F932DF07D8C001D62C9 /* Build configuration list for PBXNativeTarget "PhotoSwipeTests" */;
			buildPhases = (
				18168F782DF07D8C001D62C9 /* Sources */,
				18168F792DF07D8C001D62C9 /* Frameworks */,
				18168F7A2DF07D8C001D62C9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				18168F7E2DF07D8C001D62C9 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				18168F7F2DF07D8C001D62C9 /* PhotoSwipeTests */,
			);
			name = PhotoSwipeTests;
			packageProductDependencies = (
			);
			productName = PhotoSwipeTests;
			productReference = 18168F7C2DF07D8C001D62C9 /* PhotoSwipeTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		18168F852DF07D8C001D62C9 /* PhotoSwipeUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18168F962DF07D8C001D62C9 /* Build configuration list for PBXNativeTarget "PhotoSwipeUITests" */;
			buildPhases = (
				18168F822DF07D8C001D62C9 /* Sources */,
				18168F832DF07D8C001D62C9 /* Frameworks */,
				18168F842DF07D8C001D62C9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				18168F882DF07D8C001D62C9 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				18168F892DF07D8C001D62C9 /* PhotoSwipeUITests */,
			);
			name = PhotoSwipeUITests;
			packageProductDependencies = (
			);
			productName = PhotoSwipeUITests;
			productReference = 18168F862DF07D8C001D62C9 /* PhotoSwipeUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		18168F652DF07D8A001D62C9 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					18168F6C2DF07D8A001D62C9 = {
						CreatedOnToolsVersion = 16.3;
					};
					18168F7B2DF07D8C001D62C9 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 18168F6C2DF07D8A001D62C9;
					};
					18168F852DF07D8C001D62C9 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 18168F6C2DF07D8A001D62C9;
					};
				};
			};
			buildConfigurationList = 18168F682DF07D8A001D62C9 /* Build configuration list for PBXProject "PhotoSwipe" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 18168F642DF07D8A001D62C9;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 18168F6E2DF07D8A001D62C9 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				18168F6C2DF07D8A001D62C9 /* PhotoSwipe */,
				18168F7B2DF07D8C001D62C9 /* PhotoSwipeTests */,
				18168F852DF07D8C001D62C9 /* PhotoSwipeUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		18168F6B2DF07D8A001D62C9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18168F7A2DF07D8C001D62C9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18168F842DF07D8C001D62C9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		18168F692DF07D8A001D62C9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18168F782DF07D8C001D62C9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18168F822DF07D8C001D62C9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		18168F7E2DF07D8C001D62C9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 18168F6C2DF07D8A001D62C9 /* PhotoSwipe */;
			targetProxy = 18168F7D2DF07D8C001D62C9 /* PBXContainerItemProxy */;
		};
		18168F882DF07D8C001D62C9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 18168F6C2DF07D8A001D62C9 /* PhotoSwipe */;
			targetProxy = 18168F872DF07D8C001D62C9 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		18168F8E2DF07D8C001D62C9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		18168F8F2DF07D8C001D62C9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		18168F912DF07D8C001D62C9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "此应用需要访问您的照片库来删除不需要的图片。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "此应用需要访问您的照片库来浏览和管理图片。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.PhotoSwipe;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		18168F922DF07D8C001D62C9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "此应用需要访问您的照片库来删除不需要的图片。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "此应用需要访问您的照片库来浏览和管理图片。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.PhotoSwipe;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		18168F942DF07D8C001D62C9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.PhotoSwipeTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PhotoSwipe.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PhotoSwipe";
			};
			name = Debug;
		};
		18168F952DF07D8C001D62C9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.PhotoSwipeTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PhotoSwipe.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PhotoSwipe";
			};
			name = Release;
		};
		18168F972DF07D8C001D62C9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.PhotoSwipeUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PhotoSwipe;
			};
			name = Debug;
		};
		18168F982DF07D8C001D62C9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.PhotoSwipeUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PhotoSwipe;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		18168F682DF07D8A001D62C9 /* Build configuration list for PBXProject "PhotoSwipe" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18168F8E2DF07D8C001D62C9 /* Debug */,
				18168F8F2DF07D8C001D62C9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18168F902DF07D8C001D62C9 /* Build configuration list for PBXNativeTarget "PhotoSwipe" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18168F912DF07D8C001D62C9 /* Debug */,
				18168F922DF07D8C001D62C9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18168F932DF07D8C001D62C9 /* Build configuration list for PBXNativeTarget "PhotoSwipeTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18168F942DF07D8C001D62C9 /* Debug */,
				18168F952DF07D8C001D62C9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18168F962DF07D8C001D62C9 /* Build configuration list for PBXNativeTarget "PhotoSwipeUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18168F972DF07D8C001D62C9 /* Debug */,
				18168F982DF07D8C001D62C9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 18168F652DF07D8A001D62C9 /* Project object */;
}
