//
//  ContentView.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import SwiftUI
import Photos

struct ContentView: View {
    @State private var viewModel = PhotoSwipeViewModel()
    @State private var showingMarkedPhotosGrid = false
    @State private var showingKeptPhotosGrid = false
    @State private var isDarkMode = false
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        NavigationStack {
            ZStack {
                // 主要内容区域 - 铺满全屏
                ZStack {
                    // 背景
                    (isDarkMode ? Color.black : Color.white)
                        .ignoresSafeArea()
                    
                    if viewModel.photoService.authorizationStatus == .denied || viewModel.photoService.authorizationStatus == .restricted {
                        PermissionDeniedView()
                    } else if viewModel.photoService.isLoading {
                        LoadingView()
                    } else if viewModel.photoService.photos.isEmpty {
                        EmptyPhotosView()
                    } else if let currentPhoto = viewModel.currentPhoto {
                        SwipeablePhotoCard(
                            photo: currentPhot<PERSON>,
                            onSwipeLeft: viewModel.swipeLeft,
                            onSwipeRight: viewModel.swipeRight
                        )
                        .ignoresSafeArea()
                    }
                    
                    // 顶部状态栏 - 覆盖在图片上
                    VStack {
                        HStack {
                            // 左上角：照片索引和处理进度
                            VStack(alignment: .leading, spacing: 2) {
                                if viewModel.photoService.isLoading {
                                    Text("加载中...")
                                        .foregroundColor(isDarkMode ? .white : .black)
                                        .fontWeight(.medium)
                                } else if !viewModel.photoService.photos.isEmpty {
                                    Text("\(viewModel.currentPhotoIndex + 1) / \(viewModel.photoService.photos.count)")
                                        .foregroundColor(isDarkMode ? .white : .black)
                                        .fontWeight(.medium)
                                    
                                    Text("已处理: \(viewModel.processedPhotosCount) (删除:\(viewModel.markedPhotosCount), 保留:\(viewModel.keptPhotosCount))")
                                        .font(.caption)
                                        .foregroundColor(isDarkMode ? .white.opacity(0.8) : .black.opacity(0.7))
                                }
                            }
                            .padding(12)
                            .background((isDarkMode ? Color.black : Color.white).opacity(0.8))
                            .cornerRadius(12)
                            .shadow(radius: 4)
                            
                            Spacer()
                            
                            // 右上角：夜间模式切换和管理按钮
                            HStack(spacing: 12) {
                                // 夜间模式切换
                                Button(action: {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        isDarkMode.toggle()
                                    }
                                }) {
                                    Image(systemName: isDarkMode ? "sun.max.fill" : "moon.fill")
                                        .font(.title2)
                                        .foregroundColor(isDarkMode ? .yellow : .blue)
                                        .frame(width: 44, height: 44)
                                        .background((isDarkMode ? Color.black : Color.white).opacity(0.8))
                                        .cornerRadius(22)
                                        .shadow(radius: 4)
                                }
                                
                                // 管理按钮
                                if viewModel.markedPhotosCount > 0 || viewModel.keptPhotosCount > 0 || viewModel.hasHistory() {
                                    Menu {
                                        // 查看待删除照片
                                        let markedCount = viewModel.markedPhotosCount
                                        if markedCount > 0 {
                                            Button(action: {
                                                showingMarkedPhotosGrid = true
                                            }) {
                                                Label("查看待删除照片 (\(markedCount))", systemImage: "trash.square")
                                            }
                                        }
                                        
                                        // 查看保留照片
                                        let keptCount = viewModel.keptPhotosCount
                                        if keptCount > 0 {
                                            Button(action: {
                                                showingKeptPhotosGrid = true
                                            }) {
                                                Label("查看保留照片 (\(keptCount))", systemImage: "heart.square")
                                            }
                                        }
                                        
                                        Divider()
                                        
                                        // 删除所有标记的照片
                                        if markedCount > 0 {
                                            Button(action: viewModel.showDeleteConfirmation) {
                                                Label("删除所有标记照片", systemImage: "trash")
                                            }
                                        }
                                        
                                        // 清除所有标记
                                        if markedCount > 0 || keptCount > 0 {
                                            Button(action: {
                                                viewModel.clearAllMarks()
                                                HistoryManager.shared.clearKeptPhotos()
                                            }) {
                                                Label("清除所有标记", systemImage: "xmark.circle")
                                            }
                                        }
                                        
                                        // 历史记录统计
                                        let stats = viewModel.getExtendedHistoryStats()
                                        if stats.deletedCount > 0 || stats.keptCount > 0 {
                                            Button(action: {
                                                viewModel.clearAllRecords()
                                            }) {
                                                Label("清除所有记录", systemImage: "clock.arrow.circlepath")
                                            }
                                        }
                                    } label: {
                                        let totalCount = viewModel.markedPhotosCount + viewModel.keptPhotosCount
                                        VStack {
                                            Image(systemName: "ellipsis.circle")
                                                .font(.title2)
                                            if totalCount > 0 {
                                                Text("\(totalCount)")
                                                    .font(.caption)
                                                    .fontWeight(.bold)
                                            }
                                        }
                                        .foregroundColor(.blue)
                                        .frame(width: 44, height: 44)
                                        .background((isDarkMode ? Color.black : Color.white).opacity(0.8))
                                        .cornerRadius(22)
                                        .shadow(radius: 4)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 10)
                        
                        Spacer()
                        
                        // 底部控制按钮 - 悬浮在图片上方
                        if !viewModel.photoService.photos.isEmpty && !viewModel.photoService.isLoading {
                            GeometryReader { geometry in
                                ZStack {
                                    // 删除按钮 - 位于屏幕宽度的1/4处
                                    Button(action: {
                                        viewModel.swipeRight()
                                    }) {
                                        Image(systemName: "xmark")
                                            .font(.title)
                                            .foregroundColor(.red)
                                            .frame(width: 50, height: 50)
                                            .background((isDarkMode ? Color.black : Color.white).opacity(0.9))
                                            .cornerRadius(25)
                                            .shadow(radius: 4)
                                    }
                                    .position(x: geometry.size.width * 0.25, y: 25)
                                    
                                    // 保留按钮 - 位于屏幕宽度的3/4处
                                    Button(action: {
                                        viewModel.swipeLeft()
                                    }) {
                                        Image(systemName: "heart")
                                            .font(.title)
                                            .foregroundColor(.green)
                                            .frame(width: 50, height: 50)
                                            .background((isDarkMode ? Color.black : Color.white).opacity(0.9))
                                            .cornerRadius(25)
                                            .shadow(radius: 4)
                                    }
                                    .position(x: geometry.size.width * 0.75, y: 25)
                                }
                            }
                            .frame(height: 50)
                            .padding(.bottom, 50)
                        }
                    }
                }

                // 优化的调试信息显示（仅在Debug模式下）
                #if DEBUG
                DebugInfoOverlay(viewModel: viewModel)
                #endif
            }
            .onAppear {
                // 根据系统设置初始化夜间模式
                isDarkMode = (colorScheme == .dark)
                
                Task {
                    await viewModel.requestPermissionAndLoadPhotos()
                }
            }
            .onChange(of: colorScheme) { _, newValue in
                // 跟随系统主题变化
                isDarkMode = (newValue == .dark)
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
                // 应用从后台恢复时，使用改进的恢复处理
                DebugLogger.shared.log("📱 ContentView收到前台恢复通知", category: "UI_LIFECYCLE")
                Task {
                    // 添加短暂延迟确保UI完全恢复
                    try? await Task.sleep(nanoseconds: 200_000_000) // 0.2秒
                    await viewModel.handleAppWillEnterForeground()
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)) { _ in
                // 应用进入后台时的处理
                DebugLogger.shared.log("📱 ContentView收到后台进入通知", category: "UI_LIFECYCLE")
                viewModel.handleAppDidEnterBackground()
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
                // 应用变为活跃状态时的额外处理
                DebugLogger.shared.log("📱 ContentView收到应用激活通知", category: "UI_LIFECYCLE")
                Task {
                    // 再次确保当前照片正确显示
                    if let currentPhoto = viewModel.currentPhoto, currentPhoto.displayImage == nil {
                        DebugLogger.shared.log("🔄 应用激活时发现照片未显示，触发重新加载", category: "UI_LIFECYCLE")
                        await currentPhoto.resetAndReload()
                    }
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)) { _ in
                // 处理内存警告
                viewModel.handleMemoryWarning()
            }
            .alert("需要相册权限", isPresented: $viewModel.showingPermissionAlert) {
                Button("去设置") {
                    if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(settingsUrl)
                    }
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("请在设置中允许访问照片，以便使用此功能。")
            }
            .alert("删除照片", isPresented: $viewModel.showingDeleteConfirmation) {
                Button("删除", role: .destructive) {
                    Task {
                        await viewModel.deleteMarkedPhotos()
                    }
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("确定要删除 \(viewModel.markedPhotosCount) 张标记的照片吗？此操作无法撤销。")
            }
            .sheet(isPresented: $showingMarkedPhotosGrid) {
                MarkedPhotosGridView(viewModel: viewModel)
            }
            .sheet(isPresented: $showingKeptPhotosGrid) {
                KeptPhotosGridView(viewModel: viewModel)
            }
        }
    }
}

// MARK: - 调试信息覆盖层（仅Debug模式）
#if DEBUG
struct DebugInfoOverlay: View {
    @Bindable var viewModel: PhotoSwipeViewModel
    @State private var isExpanded = false
    @State private var showDebugInfo = true

    var body: some View {
        VStack {
            HStack {
                Spacer()

                // 调试信息面板
                if showDebugInfo {
                    VStack(alignment: .trailing, spacing: 2) {
                        // 折叠/展开按钮
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                isExpanded.toggle()
                            }
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                                    .font(.caption2)
                                Text("Debug")
                                    .font(.caption2)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.black.opacity(0.7))
                            .cornerRadius(8)
                        }

                        // 展开的调试信息
                        if isExpanded {
                            VStack(alignment: .trailing, spacing: 1) {
                                if let currentPhoto = viewModel.currentPhoto {
                                    Text(currentPhoto.validateState())
                                        .font(.system(size: 10, family: .monospaced))
                                        .foregroundColor(.white)

                                    Text("ID: \(String(currentPhoto.asset.localIdentifier.prefix(8)))...")
                                        .font(.system(size: 10, family: .monospaced))
                                        .foregroundColor(.white.opacity(0.8))
                                }

                                Text("Launch: \(UserDefaults.standard.integer(forKey: "PhotoSwipeAppLaunchCount"))")
                                    .font(.system(size: 10, family: .monospaced))
                                    .foregroundColor(.white.opacity(0.8))

                                Text("Photos: \(viewModel.photoService.photos.count)")
                                    .font(.system(size: 10, family: .monospaced))
                                    .foregroundColor(.white.opacity(0.8))
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.black.opacity(0.6))
                            .cornerRadius(6)
                            .transition(.opacity.combined(with: .scale(scale: 0.95)))
                        }
                    }
                    .transition(.opacity.combined(with: .move(edge: .trailing)))
                }

                // 隐藏/显示按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showDebugInfo.toggle()
                    }
                }) {
                    Image(systemName: showDebugInfo ? "eye.slash" : "eye")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(6)
                        .background(Color.black.opacity(0.5))
                        .clipShape(Circle())
                }
            }
            .padding(.top, 50) // 避免与状态栏重叠
            .padding(.trailing, 16)

            Spacer()
        }
    }
}
#endif

#Preview {
    ContentView()
}
