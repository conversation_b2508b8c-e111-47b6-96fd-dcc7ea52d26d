//
//  CoreDataManager.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation

/// 照片决定类型
enum PhotoDecision {
    case keep           // 保留
    case delete         // 删除
    case markForDeletion // 标记为待删除
}

/// 简化的数据管理器，使用UserDefaults作为存储
class CoreDataManager {
    static let shared = CoreDataManager()
    
    private init() {
        print("使用UserDefaults作为数据存储")
    }
    
    // MARK: - UserDefaults存储
    private let userDefaults = UserDefaults.standard
    private let markedPhotosKey = "MarkedPhotosForDeletion"
    private let deletedPhotosKey = "DeletedPhotosHistory"
    private let keptPhotosKey = "KeptPhotosHistory"
    private let currentPositionKey = "CurrentPhotoPosition"
    private let totalPhotosCountKey = "TotalPhotosCount"
    
    // MARK: - 照片记录管理
    
    /// 获取照片记录 (简化版本，返回nil)
    func getPhotoRecord(for identifier: String) -> PhotoRecord? {
        return nil // 简化版本不返回Core Data对象
    }
    
    /// 创建或更新照片记录 (简化版本)
    func createOrUpdatePhotoRecord(identifier: String, configure: (PhotoRecord) -> Void) {
        // 简化版本，不执行任何操作
    }
    
    /// 批量更新照片记录 (简化版本)
    func batchUpdatePhotoRecords(_ updates: [(String, (PhotoRecord) -> Void)]) {
        // 简化版本，不执行任何操作
    }
    
    /// 获取标记为删除的照片
    func getMarkedPhotos() -> [String] {
        return userDefaults.array(forKey: markedPhotosKey) as? [String] ?? []
    }
    
    /// 获取保留的照片
    func getKeptPhotos() -> [String] {
        return userDefaults.array(forKey: keptPhotosKey) as? [String] ?? []
    }
    
    /// 获取已删除的照片
    func getDeletedPhotos() -> [String] {
        return userDefaults.array(forKey: deletedPhotosKey) as? [String] ?? []
    }

    /// 保存照片决定
    func savePhotoDecision(photoIdentifier: String, decision: PhotoDecision) {
        print("💾 保存照片决定: \(photoIdentifier) -> \(decision)")

        // 先从所有列表中移除这张照片，避免重复
        removePhotoFromAllLists(photoIdentifier)

        switch decision {
        case .keep:
            var keptPhotos = getKeptPhotos()
            keptPhotos.append(photoIdentifier)
            userDefaults.set(keptPhotos, forKey: keptPhotosKey)
            print("✅ 照片已保存到保留列表，当前保留数量: \(keptPhotos.count)")

        case .delete:
            var deletedPhotos = getDeletedPhotos()
            deletedPhotos.append(photoIdentifier)
            userDefaults.set(deletedPhotos, forKey: deletedPhotosKey)
            print("🗑️ 照片已保存到删除列表，当前删除数量: \(deletedPhotos.count)")

        case .markForDeletion:
            var markedPhotos = getMarkedPhotos()
            markedPhotos.append(photoIdentifier)
            userDefaults.set(markedPhotos, forKey: markedPhotosKey)
            print("⚠️ 照片已标记为待删除，当前标记数量: \(markedPhotos.count)")
        }

        // 强制同步到磁盘
        userDefaults.synchronize()
    }

    /// 从所有列表中移除照片（避免重复）
    private func removePhotoFromAllLists(_ photoIdentifier: String) {
        // 从标记列表移除
        var markedPhotos = getMarkedPhotos()
        if let index = markedPhotos.firstIndex(of: photoIdentifier) {
            markedPhotos.remove(at: index)
            userDefaults.set(markedPhotos, forKey: markedPhotosKey)
        }

        // 从保留列表移除
        var keptPhotos = getKeptPhotos()
        if let index = keptPhotos.firstIndex(of: photoIdentifier) {
            keptPhotos.remove(at: index)
            userDefaults.set(keptPhotos, forKey: keptPhotosKey)
        }

        // 从删除列表移除
        var deletedPhotos = getDeletedPhotos()
        if let index = deletedPhotos.firstIndex(of: photoIdentifier) {
            deletedPhotos.remove(at: index)
            userDefaults.set(deletedPhotos, forKey: deletedPhotosKey)
        }
    }

    /// 清除所有标记
    func clearAllMarks() {
        userDefaults.removeObject(forKey: markedPhotosKey)
        userDefaults.removeObject(forKey: keptPhotosKey)
    }
    
    /// 清除所有记录
    func clearAllRecords() {
        userDefaults.removeObject(forKey: markedPhotosKey)
        userDefaults.removeObject(forKey: deletedPhotosKey)
        userDefaults.removeObject(forKey: keptPhotosKey)
        userDefaults.removeObject(forKey: currentPositionKey)
        userDefaults.removeObject(forKey: totalPhotosCountKey)
    }
    
    // MARK: - 用户会话管理
    
    /// 获取当前会话 (简化版本)
    func getCurrentSession() -> UserSession {
        let session = UserSession()
        session.currentPhotoIndex = Int32(userDefaults.integer(forKey: currentPositionKey))
        session.totalPhotosCount = Int32(userDefaults.integer(forKey: totalPhotosCountKey))
        return session
    }
    
    /// 更新会话位置
    func updateSessionPosition(index: Int, totalCount: Int) {
        userDefaults.set(index, forKey: currentPositionKey)
        userDefaults.set(totalCount, forKey: totalPhotosCountKey)
    }
    
    // MARK: - 统计信息
    
    func getStatistics() -> (markedCount: Int, keptCount: Int, deletedCount: Int) {
        let markedCount = getMarkedPhotos().count
        let keptCount = getKeptPhotos().count
        let deletedCount = getDeletedPhotos().count
        return (markedCount, keptCount, deletedCount)
    }
}

// MARK: - 简化的模型类

class PhotoRecord {
    var photoIdentifier: String = ""
    var isMarkedForDeletion: Bool = false
    var isKept: Bool = false
    var isDeleted: Bool = false
    var createdAt: Date?
    var lastModified: Date?
    var processingOrder: Int32 = 0
}

class UserSession {
    var sessionId: String = UUID().uuidString
    var currentPhotoIndex: Int32 = 0
    var totalPhotosCount: Int32 = 0
    var lastActiveDate: Date?
}
