//
//  CoreDataManager.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation

/// 简化的数据管理器，使用UserDefaults作为存储
class CoreDataManager {
    static let shared = CoreDataManager()

    private init() {
        print("使用UserDefaults作为数据存储")
    }

    // MARK: - UserDefaults存储
    private let userDefaults = UserDefaults.standard
    private let markedPhotosKey = "MarkedPhotosForDeletion"
    private let deletedPhotosKey = "DeletedPhotosHistory"
    private let keptPhotosKey = "KeptPhotosHistory"
    private let currentPositionKey = "CurrentPhotoPosition"
    private let totalPhotosCountKey = "TotalPhotosCount"

    // MARK: - 照片记录管理

    /// 获取照片记录 (简化版本，返回nil)
    func getPhotoRecord(for identifier: String) -> PhotoRecord? {
        return nil // 简化版本不返回Core Data对象
    }

    /// 创建或更新照片记录 (简化版本)
    func createOrUpdatePhotoRecord(identifier: String, configure: (PhotoRecord) -> Void) {
        // 简化版本，不执行任何操作
    }

    /// 批量更新照片记录 (简化版本)
    func batchUpdatePhotoRecords(_ updates: [(String, (PhotoRecord) -> Void)]) {
        // 简化版本，不执行任何操作
    }

    /// 获取标记为删除的照片
    func getMarkedPhotos() -> [String] {
        return userDefaults.array(forKey: markedPhotosKey) as? [String] ?? []
    }

    /// 获取保留的照片
    func getKeptPhotos() -> [String] {
        return userDefaults.array(forKey: keptPhotosKey) as? [String] ?? []
    }

    /// 获取已删除的照片
    func getDeletedPhotos() -> [String] {
        return userDefaults.array(forKey: deletedPhotosKey) as? [String] ?? []
    }

    /// 清除所有标记
    func clearAllMarks() {
        userDefaults.removeObject(forKey: markedPhotosKey)
        userDefaults.removeObject(forKey: keptPhotosKey)
    }

    /// 清除所有记录
    func clearAllRecords() {
        userDefaults.removeObject(forKey: markedPhotosKey)
        userDefaults.removeObject(forKey: deletedPhotosKey)
        userDefaults.removeObject(forKey: keptPhotosKey)
        userDefaults.removeObject(forKey: currentPositionKey)
        userDefaults.removeObject(forKey: totalPhotosCountKey)
    }

    // MARK: - 用户会话管理

    /// 获取当前会话 (简化版本)
    func getCurrentSession() -> UserSession {
        // 返回一个模拟的会话对象
        return UserSession()
    }

    /// 更新会话位置
    func updateSessionPosition(index: Int, totalCount: Int) {
        userDefaults.set(index, forKey: currentPositionKey)
        userDefaults.set(totalCount, forKey: totalPhotosCountKey)
    }

    // MARK: - 统计信息

    func getStatistics() -> (markedCount: Int, keptCount: Int, deletedCount: Int) {
        let markedCount = getMarkedPhotos().count
        let keptCount = getKeptPhotos().count
        let deletedCount = getDeletedPhotos().count
        return (markedCount, keptCount, deletedCount)
    }
}

// MARK: - 简化的模型类

class PhotoRecord {
    var photoIdentifier: String = ""
    var isMarkedForDeletion: Bool = false
    var isKept: Bool = false
    var isDeleted: Bool = false
    var createdAt: Date?
    var lastModified: Date?
    var processingOrder: Int32 = 0
}

class UserSession {
    var sessionId: String = UUID().uuidString
    var currentPhotoIndex: Int32 = 0
    var totalPhotosCount: Int32 = 0
    var lastActiveDate: Date?
}
    private let markedPhotosKey = "MarkedPhotosForDeletion"
    private let deletedPhotosKey = "DeletedPhotosHistory"
    private let keptPhotosKey = "KeptPhotosHistory"
    private let currentPositionKey = "CurrentPhotoPosition"
    private let totalPhotosCountKey = "TotalPhotosCount"
    
    var viewContext: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    // MARK: - 保存上下文
    
    func saveContext() {
        let context = persistentContainer.viewContext
        
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                print("Core Data保存失败: \(error)")
                // 可以在这里添加错误处理逻辑
            }
        }
    }
    
    func saveContextAsync() async {
        await withCheckedContinuation { continuation in
            persistentContainer.performBackgroundTask { context in
                do {
                    if context.hasChanges {
                        try context.save()
                    }
                    continuation.resume()
                } catch {
                    print("Core Data异步保存失败: \(error)")
                    continuation.resume()
                }
            }
        }
    }
    
    // MARK: - 照片记录管理
    
    /// 获取照片记录
    func getPhotoRecord(for identifier: String) -> PhotoRecord? {
        if !isCoreDataAvailable {
            return nil // UserDefaults模式下不返回PhotoRecord对象
        }

        let request: NSFetchRequest<PhotoRecord> = PhotoRecord.fetchRequest()
        request.predicate = NSPredicate(format: "photoIdentifier == %@", identifier)
        request.fetchLimit = 1

        do {
            let results = try viewContext.fetch(request)
            return results.first
        } catch {
            print("获取照片记录失败: \(error)")
            return nil
        }
    }
    
    /// 创建或更新照片记录
    func createOrUpdatePhotoRecord(identifier: String, configure: (PhotoRecord) -> Void) {
        let record = getPhotoRecord(for: identifier) ?? PhotoRecord(context: viewContext)
        
        if record.photoIdentifier != identifier {
            record.photoIdentifier = identifier
            record.createdAt = Date()
        }
        
        record.lastModified = Date()
        configure(record)
        
        saveContext()
    }
    
    /// 批量更新照片记录
    func batchUpdatePhotoRecords(_ updates: [(String, (PhotoRecord) -> Void)]) {
        persistentContainer.performBackgroundTask { context in
            for (identifier, configure) in updates {
                let request: NSFetchRequest<PhotoRecord> = PhotoRecord.fetchRequest()
                request.predicate = NSPredicate(format: "photoIdentifier == %@", identifier)
                request.fetchLimit = 1
                
                do {
                    let results = try context.fetch(request)
                    let record = results.first ?? PhotoRecord(context: context)
                    
                    if record.photoIdentifier != identifier {
                        record.photoIdentifier = identifier
                        record.createdAt = Date()
                    }
                    
                    record.lastModified = Date()
                    configure(record)
                } catch {
                    print("批量更新照片记录失败: \(error)")
                }
            }
            
            do {
                if context.hasChanges {
                    try context.save()
                }
            } catch {
                print("批量保存失败: \(error)")
            }
        }
    }
    
    /// 获取标记为删除的照片
    func getMarkedPhotos() -> [String] {
        if !isCoreDataAvailable {
            // 使用UserDefaults后备
            return userDefaults.array(forKey: markedPhotosKey) as? [String] ?? []
        }

        let request: NSFetchRequest<PhotoRecord> = PhotoRecord.fetchRequest()
        request.predicate = NSPredicate(format: "isMarkedForDeletion == YES")
        request.sortDescriptors = [NSSortDescriptor(key: "lastModified", ascending: false)]

        do {
            let results = try viewContext.fetch(request)
            return results.compactMap { $0.photoIdentifier }
        } catch {
            print("获取标记照片失败: \(error)")
            // 回退到UserDefaults
            return userDefaults.array(forKey: markedPhotosKey) as? [String] ?? []
        }
    }
    
    /// 获取保留的照片
    func getKeptPhotos() -> [String] {
        let request: NSFetchRequest<PhotoRecord> = PhotoRecord.fetchRequest()
        request.predicate = NSPredicate(format: "isKept == YES")
        request.sortDescriptors = [NSSortDescriptor(key: "lastModified", ascending: false)]
        
        do {
            let results = try viewContext.fetch(request)
            return results.compactMap { $0.photoIdentifier }
        } catch {
            print("获取保留照片失败: \(error)")
            return []
        }
    }
    
    /// 获取已删除的照片
    func getDeletedPhotos() -> [String] {
        let request: NSFetchRequest<PhotoRecord> = PhotoRecord.fetchRequest()
        request.predicate = NSPredicate(format: "isDeleted == YES")
        request.sortDescriptors = [NSSortDescriptor(key: "lastModified", ascending: false)]
        
        do {
            let results = try viewContext.fetch(request)
            return results.compactMap { $0.photoIdentifier }
        } catch {
            print("获取已删除照片失败: \(error)")
            return []
        }
    }
    
    /// 清除所有标记
    func clearAllMarks() {
        let request: NSFetchRequest<PhotoRecord> = PhotoRecord.fetchRequest()
        request.predicate = NSPredicate(format: "isMarkedForDeletion == YES OR isKept == YES")
        
        do {
            let results = try viewContext.fetch(request)
            for record in results {
                record.isMarkedForDeletion = false
                record.isKept = false
                record.lastModified = Date()
            }
            saveContext()
        } catch {
            print("清除标记失败: \(error)")
        }
    }
    
    /// 清除所有记录
    func clearAllRecords() {
        let photoRequest: NSFetchRequest<NSFetchRequestResult> = PhotoRecord.fetchRequest()
        let photoDeleteRequest = NSBatchDeleteRequest(fetchRequest: photoRequest)
        
        let sessionRequest: NSFetchRequest<NSFetchRequestResult> = UserSession.fetchRequest()
        let sessionDeleteRequest = NSBatchDeleteRequest(fetchRequest: sessionRequest)
        
        do {
            try viewContext.execute(photoDeleteRequest)
            try viewContext.execute(sessionDeleteRequest)
            saveContext()
        } catch {
            print("清除所有记录失败: \(error)")
        }
    }
    
    // MARK: - 用户会话管理
    
    /// 获取当前会话
    func getCurrentSession() -> UserSession {
        let request: NSFetchRequest<UserSession> = UserSession.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(key: "lastActiveDate", ascending: false)]
        request.fetchLimit = 1
        
        do {
            let results = try viewContext.fetch(request)
            if let session = results.first {
                session.lastActiveDate = Date()
                saveContext()
                return session
            }
        } catch {
            print("获取会话失败: \(error)")
        }
        
        // 创建新会话
        let newSession = UserSession(context: viewContext)
        newSession.sessionId = UUID().uuidString
        newSession.lastActiveDate = Date()
        newSession.currentPhotoIndex = 0
        newSession.totalPhotosCount = 0
        saveContext()
        
        return newSession
    }
    
    /// 更新会话位置
    func updateSessionPosition(index: Int, totalCount: Int) {
        let session = getCurrentSession()
        session.currentPhotoIndex = Int32(index)
        session.totalPhotosCount = Int32(totalCount)
        session.lastActiveDate = Date()
        saveContext()
    }
    
    // MARK: - 统计信息
    
    func getStatistics() -> (markedCount: Int, keptCount: Int, deletedCount: Int) {
        let markedRequest: NSFetchRequest<PhotoRecord> = PhotoRecord.fetchRequest()
        markedRequest.predicate = NSPredicate(format: "isMarkedForDeletion == YES")
        
        let keptRequest: NSFetchRequest<PhotoRecord> = PhotoRecord.fetchRequest()
        keptRequest.predicate = NSPredicate(format: "isKept == YES")
        
        let deletedRequest: NSFetchRequest<PhotoRecord> = PhotoRecord.fetchRequest()
        deletedRequest.predicate = NSPredicate(format: "isDeleted == YES")
        
        do {
            let markedCount = try viewContext.count(for: markedRequest)
            let keptCount = try viewContext.count(for: keptRequest)
            let deletedCount = try viewContext.count(for: deletedRequest)
            
            return (markedCount, keptCount, deletedCount)
        } catch {
            print("获取统计信息失败: \(error)")
            return (0, 0, 0)
        }
    }
    
    // MARK: - 错误处理
    
    private func handlePersistentStoreError(_ error: NSError) {
        print("Core Data存储错误: \(error)")
        
        // 删除存储文件并重新创建
        let storeURL = persistentContainer.persistentStoreDescriptions.first?.url
        if let url = storeURL {
            do {
                try FileManager.default.removeItem(at: url)
                print("已删除损坏的Core Data存储文件")
            } catch {
                print("删除存储文件失败: \(error)")
            }
        }
    }
    
    // MARK: - 数据迁移
    
    private func migrateFromUserDefaults() {
        let userDefaults = UserDefaults.standard
        let migrationKey = "CoreDataMigrationCompleted"
        
        // 检查是否已经迁移
        if userDefaults.bool(forKey: migrationKey) {
            return
        }
        
        print("开始从UserDefaults迁移数据到Core Data...")
        
        // 迁移标记的照片
        if let markedPhotos = userDefaults.array(forKey: "MarkedPhotosForDeletion") as? [String] {
            for photoId in markedPhotos {
                createOrUpdatePhotoRecord(identifier: photoId) { record in
                    record.isMarkedForDeletion = true
                }
            }
        }
        
        // 迁移保留的照片
        if let keptPhotos = userDefaults.array(forKey: "KeptPhotosHistory") as? [String] {
            for photoId in keptPhotos {
                createOrUpdatePhotoRecord(identifier: photoId) { record in
                    record.isKept = true
                }
            }
        }
        
        // 迁移已删除的照片
        if let deletedPhotos = userDefaults.array(forKey: "DeletedPhotosHistory") as? [String] {
            for photoId in deletedPhotos {
                createOrUpdatePhotoRecord(identifier: photoId) { record in
                    record.isDeleted = true
                }
            }
        }
        
        // 迁移会话信息
        let currentIndex = userDefaults.integer(forKey: "CurrentPhotoPosition")
        let totalCount = userDefaults.integer(forKey: "TotalPhotosCount")
        if currentIndex > 0 || totalCount > 0 {
            updateSessionPosition(index: currentIndex, totalCount: totalCount)
        }
        
        // 标记迁移完成
        userDefaults.set(true, forKey: migrationKey)
        
        print("数据迁移完成")
    }
}
