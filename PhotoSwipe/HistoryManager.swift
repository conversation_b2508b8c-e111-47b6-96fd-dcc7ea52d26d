//
//  HistoryManager.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation
import Photos

/// 历史记录管理器，使用Core Data进行高效数据管理
class HistoryManager {
    static let shared = HistoryManager()

    private let coreDataManager = CoreDataManager.shared

    private init() {}
    
    /// 保存标记为删除的照片
    func saveMarkedPhoto(_ photoIdentifier: String) {
        coreDataManager.createOrUpdatePhotoRecord(identifier: photoIdentifier) { record in
            record.isMarkedForDeletion = true
        }
    }

    /// 移除删除标记
    func removeMarkForDeletion(photoId: String) {
        coreDataManager.createOrUpdatePhotoRecord(identifier: photoId) { record in
            record.isMarkedForDeletion = false
        }
    }

    /// 获取所有标记为删除的照片标识符
    func getMarkedPhotos() -> Set<String> {
        return Set(coreDataManager.getMarkedPhotos())
    }

    /// 检查照片是否被标记为删除
    func isPhotoMarked(_ photoIdentifier: String) -> Bool {
        return coreDataManager.getPhotoRecord(for: photoIdentifier)?.isMarkedForDeletion ?? false
    }
    
    /// 保存已删除的照片到历史记录
    func saveDeletedPhotos(_ photoIdentifiers: [String]) {
        let updates = photoIdentifiers.map { identifier in
            (identifier, { (record: PhotoRecord) in
                record.isDeleted = true
                record.isMarkedForDeletion = false // 清除删除标记
            })
        }
        coreDataManager.batchUpdatePhotoRecords(updates)
    }

    /// 保存单个已删除的照片到历史记录
    func saveDeletedPhoto(photoId: String) {
        saveDeletedPhotos([photoId])
    }

    /// 获取已删除的照片历史记录
    func getDeletedPhotos() -> [String] {
        return coreDataManager.getDeletedPhotos()
    }
    
    /// 清除所有历史记录
    func clearAllHistory() {
        coreDataManager.clearAllRecords()
    }

    /// 清除标记记录
    func clearMarkedPhotos() {
        coreDataManager.clearAllMarks()
    }

    /// 清除所有标记
    func clearAllMarks() {
        coreDataManager.clearAllMarks()
    }

    /// 获取历史记录统计信息
    func getHistoryStats() -> (markedCount: Int, deletedCount: Int) {
        let stats = coreDataManager.getStatistics()
        return (stats.markedCount, stats.deletedCount)
    }
    
    // MARK: - 保留图片管理
    
    /// 保存保留的照片
    func saveKeptPhoto(_ photoIdentifier: String) {
        coreDataManager.createOrUpdatePhotoRecord(identifier: photoIdentifier) { record in
            record.isKept = true
        }
    }

    /// 移除保留标记
    func removeKeptPhoto(_ photoIdentifier: String) {
        coreDataManager.createOrUpdatePhotoRecord(identifier: photoIdentifier) { record in
            record.isKept = false
        }
    }

    /// 获取所有保留的照片标识符
    func getKeptPhotos() -> Set<String> {
        return Set(coreDataManager.getKeptPhotos())
    }

    /// 检查照片是否被标记为保留
    func isPhotoKept(_ photoIdentifier: String) -> Bool {
        return coreDataManager.getPhotoRecord(for: photoIdentifier)?.isKept ?? false
    }

    /// 清除保留记录
    func clearKeptPhotos() {
        // 这个方法需要更新所有保留的照片记录
        let keptPhotos = coreDataManager.getKeptPhotos()
        let updates = keptPhotos.map { identifier in
            (identifier, { (record: PhotoRecord) in
                record.isKept = false
            })
        }
        coreDataManager.batchUpdatePhotoRecords(updates)
    }
    
    // MARK: - 位置记录管理
    
    /// 保存当前浏览位置
    func saveCurrentPosition(_ index: Int, totalCount: Int) {
        coreDataManager.updateSessionPosition(index: index, totalCount: totalCount)
    }

    /// 获取上次保存的位置
    func getSavedPosition() -> (index: Int, totalCount: Int) {
        let session = coreDataManager.getCurrentSession()
        return (Int(session.currentPhotoIndex), Int(session.totalPhotosCount))
    }

    /// 清除位置记录
    func clearSavedPosition() {
        coreDataManager.updateSessionPosition(index: 0, totalCount: 0)
    }

    /// 获取扩展的历史记录统计信息
    func getExtendedHistoryStats() -> (markedCount: Int, deletedCount: Int, keptCount: Int) {
        return coreDataManager.getStatistics()
    }

    /// 清除所有记录（包括保留记录和位置）
    func clearAllRecords() {
        coreDataManager.clearAllRecords()
    }
}