//
//  HistoryManager.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation
import Photos

/// 历史记录管理器，负责持久化用户的删除标记、保留记录和浏览位置
class HistoryManager {
    static let shared = HistoryManager()
    
    private let userDefaults = UserDefaults.standard
    private let markedPhotosKey = "MarkedPhotosForDeletion"
    private let deletedPhotosKey = "DeletedPhotosHistory"
    private let keptPhotosKey = "KeptPhotosHistory"
    private let currentPositionKey = "CurrentPhotoPosition"
    private let totalPhotosCountKey = "TotalPhotosCount"
    
    private init() {}
    
    /// 保存标记为删除的照片
    func saveMarkedPhoto(_ photoIdentifier: String) {
        var markedPhotos = getMarkedPhotos()
        markedPhotos.insert(photoIdentifier)
        userDefaults.set(Array(markedPhotos), forKey: markedPhotosKey)
    }
    
    /// 移除删除标记
    func removeMarkForDeletion(photoId: String) {
        var markedPhotos = getMarkedPhotos()
        markedPhotos.remove(photoId)
        userDefaults.set(Array(markedPhotos), forKey: markedPhotosKey)
    }
    
    /// 获取所有标记为删除的照片标识符
    func getMarkedPhotos() -> Set<String> {
        let array = userDefaults.array(forKey: markedPhotosKey) as? [String] ?? []
        return Set(array)
    }
    
    /// 检查照片是否被标记为删除
    func isPhotoMarked(_ photoIdentifier: String) -> Bool {
        return getMarkedPhotos().contains(photoIdentifier)
    }
    
    /// 保存已删除的照片到历史记录
    func saveDeletedPhotos(_ photoIdentifiers: [String]) {
        var deletedPhotos = getDeletedPhotos()
        deletedPhotos.append(contentsOf: photoIdentifiers)
        userDefaults.set(deletedPhotos, forKey: deletedPhotosKey)
        
        // 从标记列表中移除已删除的照片
        var markedPhotos = getMarkedPhotos()
        for identifier in photoIdentifiers {
            markedPhotos.remove(identifier)
        }
        userDefaults.set(Array(markedPhotos), forKey: markedPhotosKey)
    }
    
    /// 保存单个已删除的照片到历史记录
    func saveDeletedPhoto(photoId: String) {
        saveDeletedPhotos([photoId])
    }
    
    /// 获取已删除的照片历史记录
    func getDeletedPhotos() -> [String] {
        return userDefaults.array(forKey: deletedPhotosKey) as? [String] ?? []
    }
    
    /// 清除所有历史记录
    func clearAllHistory() {
        userDefaults.removeObject(forKey: markedPhotosKey)
        userDefaults.removeObject(forKey: deletedPhotosKey)
    }
    
    /// 清除标记记录
    func clearMarkedPhotos() {
        userDefaults.removeObject(forKey: markedPhotosKey)
    }
    
    /// 清除所有标记
    func clearAllMarks() {
        clearMarkedPhotos()
    }
    
    /// 获取历史记录统计信息
    func getHistoryStats() -> (markedCount: Int, deletedCount: Int) {
        return (getMarkedPhotos().count, getDeletedPhotos().count)
    }
    
    // MARK: - 保留图片管理
    
    /// 保存保留的照片
    func saveKeptPhoto(_ photoIdentifier: String) {
        var keptPhotos = getKeptPhotos()
        keptPhotos.insert(photoIdentifier)
        userDefaults.set(Array(keptPhotos), forKey: keptPhotosKey)
    }
    
    /// 移除保留标记
    func removeKeptPhoto(_ photoIdentifier: String) {
        var keptPhotos = getKeptPhotos()
        keptPhotos.remove(photoIdentifier)
        userDefaults.set(Array(keptPhotos), forKey: keptPhotosKey)
    }
    
    /// 获取所有保留的照片标识符
    func getKeptPhotos() -> Set<String> {
        let array = userDefaults.array(forKey: keptPhotosKey) as? [String] ?? []
        return Set(array)
    }
    
    /// 检查照片是否被标记为保留
    func isPhotoKept(_ photoIdentifier: String) -> Bool {
        return getKeptPhotos().contains(photoIdentifier)
    }
    
    /// 清除保留记录
    func clearKeptPhotos() {
        userDefaults.removeObject(forKey: keptPhotosKey)
    }
    
    // MARK: - 位置记录管理
    
    /// 保存当前浏览位置
    func saveCurrentPosition(_ index: Int, totalCount: Int) {
        userDefaults.set(index, forKey: currentPositionKey)
        userDefaults.set(totalCount, forKey: totalPhotosCountKey)
    }
    
    /// 获取上次保存的位置
    func getSavedPosition() -> (index: Int, totalCount: Int) {
        let index = userDefaults.integer(forKey: currentPositionKey)
        let totalCount = userDefaults.integer(forKey: totalPhotosCountKey)
        return (index, totalCount)
    }
    
    /// 清除位置记录
    func clearSavedPosition() {
        userDefaults.removeObject(forKey: currentPositionKey)
        userDefaults.removeObject(forKey: totalPhotosCountKey)
    }
    
    /// 获取扩展的历史记录统计信息
    func getExtendedHistoryStats() -> (markedCount: Int, deletedCount: Int, keptCount: Int) {
        return (getMarkedPhotos().count, getDeletedPhotos().count, getKeptPhotos().count)
    }
    
    /// 清除所有记录（包括保留记录和位置）
    func clearAllRecords() {
        clearAllHistory()
        clearKeptPhotos()
        clearSavedPosition()
    }
}