//
//  ImageCacheManager.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation
import UIKit

/// 智能图片缓存管理器，解决内存泄漏和优化缓存策略
class ImageCacheManager {
    static let shared = ImageCacheManager()
    
    // MARK: - 缓存配置
    private struct CacheConfig {
        static let memoryCountLimit = 50 // 减少内存缓存数量
        static let memoryCostLimit = 50 * 1024 * 1024 // 50MB内存限制
        static let diskCacheSize = 200 * 1024 * 1024 // 200MB磁盘缓存
        static let maxCacheAge: TimeInterval = 7 * 24 * 60 * 60 // 7天
    }
    
    // MARK: - 缓存实例
    private let memoryCache: NSCache<NSString, CachedImage>
    private let diskCacheURL: URL
    private let cacheQueue = DispatchQueue(label: "com.photoswipe.cache", qos: .utility)
    private let memoryWarningObserver: NSObjectProtocol
    
    // MARK: - 内存监控
    private var currentMemoryUsage: Int64 = 0
    private let maxMemoryUsage: Int64 = 50 * 1024 * 1024 // 50MB
    
    private init() {
        // 初始化内存缓存
        memoryCache = NSCache<NSString, CachedImage>()
        memoryCache.countLimit = CacheConfig.memoryCountLimit
        memoryCache.totalCostLimit = CacheConfig.memoryCostLimit
        memoryCache.delegate = self
        
        // 初始化磁盘缓存目录
        let cacheDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        diskCacheURL = cacheDirectory.appendingPathComponent("PhotoSwipeImageCache")
        
        // 创建缓存目录
        try? FileManager.default.createDirectory(at: diskCacheURL, withIntermediateDirectories: true)
        
        // 监听内存警告
        memoryWarningObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleMemoryWarning()
        }
        
        // 启动时清理过期缓存
        cleanExpiredCache()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(memoryWarningObserver)
    }
    
    // MARK: - 缓存操作
    
    /// 获取缓存的图片
    func getCachedImage(for key: String, type: ImageType) -> UIImage? {
        let cacheKey = generateCacheKey(key: key, type: type)
        
        // 先检查内存缓存
        if let cachedImage = memoryCache.object(forKey: NSString(string: cacheKey)) {
            cachedImage.lastAccessTime = Date()
            return cachedImage.image
        }
        
        // 检查磁盘缓存
        return loadImageFromDisk(cacheKey: cacheKey)
    }
    
    /// 缓存图片
    func cacheImage(_ image: UIImage, for key: String, type: ImageType) {
        let cacheKey = generateCacheKey(key: key, type: type)
        let cost = calculateImageCost(image)
        
        // 检查内存使用情况
        if currentMemoryUsage + Int64(cost) > maxMemoryUsage {
            clearLeastRecentlyUsedImages()
        }
        
        let cachedImage = CachedImage(image: image, cost: cost)
        
        // 存储到内存缓存
        memoryCache.setObject(cachedImage, forKey: NSString(string: cacheKey), cost: cost)
        currentMemoryUsage += Int64(cost)
        
        // 异步存储到磁盘缓存
        cacheQueue.async { [weak self] in
            self?.saveImageToDisk(image: image, cacheKey: cacheKey)
        }
    }
    
    /// 清除指定图片的缓存
    func removeCachedImage(for key: String, type: ImageType) {
        let cacheKey = generateCacheKey(key: key, type: type)
        
        // 从内存缓存移除
        if let cachedImage = memoryCache.object(forKey: NSString(string: cacheKey)) {
            currentMemoryUsage -= Int64(cachedImage.cost)
        }
        memoryCache.removeObject(forKey: NSString(string: cacheKey))
        
        // 从磁盘缓存移除
        cacheQueue.async { [weak self] in
            self?.removeImageFromDisk(cacheKey: cacheKey)
        }
    }
    
    /// 清除所有缓存
    func clearAllCache() {
        // 清除内存缓存
        memoryCache.removeAllObjects()
        currentMemoryUsage = 0
        
        // 清除磁盘缓存
        cacheQueue.async { [weak self] in
            guard let self = self else { return }
            try? FileManager.default.removeItem(at: self.diskCacheURL)
            try? FileManager.default.createDirectory(at: self.diskCacheURL, withIntermediateDirectories: true)
        }
    }
    
    // MARK: - 内存管理
    
    private func handleMemoryWarning() {
        // 内存警告时，清除一半的内存缓存
        let currentCount = memoryCache.countLimit
        memoryCache.countLimit = currentCount / 2
        
        // 重置限制
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.memoryCache.countLimit = CacheConfig.memoryCountLimit
        }
        
        currentMemoryUsage = 0 // 重置计数
    }
    
    private func clearLeastRecentlyUsedImages() {
        // 清除最近最少使用的图片
        memoryCache.countLimit = memoryCache.countLimit / 2
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.memoryCache.countLimit = CacheConfig.memoryCountLimit
        }
        
        currentMemoryUsage = currentMemoryUsage / 2
    }
    
    // MARK: - 磁盘缓存操作
    
    private func loadImageFromDisk(cacheKey: String) -> UIImage? {
        let fileURL = diskCacheURL.appendingPathComponent(cacheKey)
        
        guard FileManager.default.fileExists(atPath: fileURL.path),
              let data = try? Data(contentsOf: fileURL),
              let image = UIImage(data: data) else {
            return nil
        }
        
        // 加载到内存缓存
        let cost = calculateImageCost(image)
        let cachedImage = CachedImage(image: image, cost: cost)
        memoryCache.setObject(cachedImage, forKey: NSString(string: cacheKey), cost: cost)
        
        return image
    }
    
    private func saveImageToDisk(image: UIImage, cacheKey: String) {
        let fileURL = diskCacheURL.appendingPathComponent(cacheKey)
        
        guard let data = image.jpegData(compressionQuality: 0.8) else { return }
        
        try? data.write(to: fileURL)
    }
    
    private func removeImageFromDisk(cacheKey: String) {
        let fileURL = diskCacheURL.appendingPathComponent(cacheKey)
        try? FileManager.default.removeItem(at: fileURL)
    }
    
    // MARK: - 缓存清理
    
    private func cleanExpiredCache() {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }
            
            let fileManager = FileManager.default
            guard let files = try? fileManager.contentsOfDirectory(at: self.diskCacheURL, includingPropertiesForKeys: [.contentModificationDateKey]) else {
                return
            }
            
            let expiredDate = Date().addingTimeInterval(-CacheConfig.maxCacheAge)
            
            for fileURL in files {
                if let attributes = try? fileManager.attributesOfItem(atPath: fileURL.path),
                   let modificationDate = attributes[.modificationDate] as? Date,
                   modificationDate < expiredDate {
                    try? fileManager.removeItem(at: fileURL)
                }
            }
        }
    }
    
    // MARK: - 辅助方法
    
    private func generateCacheKey(key: String, type: ImageType) -> String {
        return "\(type.rawValue)_\(key)"
    }
    
    private func calculateImageCost(_ image: UIImage) -> Int {
        let pixelCount = Int(image.size.width * image.size.height * image.scale * image.scale)
        return pixelCount * 4 // 假设每个像素4字节
    }
}

// MARK: - NSCacheDelegate

extension ImageCacheManager: NSCacheDelegate {
    func cache(_ cache: NSCache<AnyObject, AnyObject>, willEvictObject obj: AnyObject) {
        if let cachedImage = obj as? CachedImage {
            currentMemoryUsage -= Int64(cachedImage.cost)
        }
    }
}

// MARK: - 支持类型

enum ImageType: String {
    case thumbnail = "thumb"
    case preview = "preview"
    case fullSize = "full"
}

private class CachedImage {
    let image: UIImage
    let cost: Int
    var lastAccessTime: Date
    
    init(image: UIImage, cost: Int) {
        self.image = image
        self.cost = cost
        self.lastAccessTime = Date()
    }
}
