//
//  KeptPhotosGridView.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import SwiftUI
import Photos

struct KeptPhotosGridView: View {
    let viewModel: PhotoSwipeViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var selectedPhotos: Set<PhotoModel> = []
    @State private var showingDeleteConfirmation = false
    
    private let columns = [
        GridItem(.adaptive(minimum: 120))
    ]
    
    var keptPhotos: [PhotoModel] {
        viewModel.getKeptPhotos()
    }
    
    var body: some View {
        NavigationView {
            VStack {
                if keptPhotos.isEmpty {
                    ContentUnavailableView(
                        "没有保留的照片",
                        systemImage: "heart.slash",
                        description: Text("您还没有保留任何照片")
                    )
                } else {
                    ScrollView {
                        LazyVGrid(columns: columns, spacing: 10) {
                            ForEach(keptPhotos, id: \.id) { photo in
                                KeptPhotoGridItem(
                                    photo: photo,
                                    isSelected: selectedPhotos.contains(photo),
                                    onTap: {
                                        if selectedPhotos.contains(photo) {
                                            selectedPhotos.remove(photo)
                                        } else {
                                            selectedPhotos.insert(photo)
                                        }
                                    }
                                )
                            }
                        }
                        .padding()
                    }
                    
                    // 底部操作按钮
                    if !selectedPhotos.isEmpty {
                        VStack(spacing: 12) {
                            Divider()
                            
                            HStack(spacing: 20) {
                                // 取消保留
                                Button(action: {
                                    for photo in selectedPhotos {
                                        viewModel.resetPhotoKeepMark(photo)
                                    }
                                    selectedPhotos.removeAll()
                                }) {
                                    Label("取消保留", systemImage: "heart.slash")
                                        .foregroundColor(.orange)
                                        .padding(.horizontal, 20)
                                        .padding(.vertical, 10)
                                        .background(Color.orange.opacity(0.1))
                                        .cornerRadius(8)
                                }
                                
                                // 标记删除
                                Button(action: {
                                    showingDeleteConfirmation = true
                                }) {
                                    Label("标记删除", systemImage: "trash")
                                        .foregroundColor(.red)
                                        .padding(.horizontal, 20)
                                        .padding(.vertical, 10)
                                        .background(Color.red.opacity(0.1))
                                        .cornerRadius(8)
                                }
                            }
                            .padding(.horizontal)
                            .padding(.bottom)
                        }
                        .background(Color(.systemBackground))
                    }
                }
            }
            .navigationTitle("保留的照片")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                if !keptPhotos.isEmpty {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(selectedPhotos.count == keptPhotos.count ? "取消全选" : "全选") {
                            if selectedPhotos.count == keptPhotos.count {
                                selectedPhotos.removeAll()
                            } else {
                                selectedPhotos = Set(keptPhotos)
                            }
                        }
                    }
                }
            }
            .alert("标记删除", isPresented: $showingDeleteConfirmation) {
                Button("确定", role: .destructive) {
                    for photo in selectedPhotos {
                        // 取消保留标记
                        viewModel.resetPhotoKeepMark(photo)
                        // 标记为删除
                        photo.isMarkedForDeletion = true
                    }
                    selectedPhotos.removeAll()
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("确定要将选中的 \(selectedPhotos.count) 张照片标记为删除吗？")
            }
        }
    }
}

struct KeptPhotoGridItem: View {
    let photo: PhotoModel
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        ZStack {
            AsyncImage(url: nil) { _ in
                if let image = photo.image {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 120, height: 120)
                        .clipped()
                        .cornerRadius(8)
                } else {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 120, height: 120)
                        .cornerRadius(8)
                        .overlay {
                            ProgressView()
                        }
                        .onAppear {
                            Task {
                                await photo.loadImage()
                            }
                        }
                }
            }
            
            // 选中状态覆盖层
            if isSelected {
                Rectangle()
                    .fill(Color.blue.opacity(0.3))
                    .frame(width: 120, height: 120)
                    .cornerRadius(8)
                
                VStack {
                    HStack {
                        Spacer()
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.blue)
                            .background(Color.white)
                            .clipShape(Circle())
                            .padding(4)
                    }
                    Spacer()
                }
            }
            
            // 保留标记
            VStack {
                HStack {
                    Image(systemName: "heart.fill")
                        .foregroundColor(.red)
                        .background(Color.white.opacity(0.8))
                        .clipShape(Circle())
                        .padding(4)
                    Spacer()
                }
                Spacer()
            }
        }
        .onTapGesture {
            onTap()
        }
    }
}

#Preview {
    KeptPhotosGridView(viewModel: PhotoSwipeViewModel())
}