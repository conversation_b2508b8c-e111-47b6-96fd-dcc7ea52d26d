//
//  PerformantImageLoader.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation
import Photos
import UIKit

/// 高性能图片加载器，优化加载速度和用户体验
class PerformantImageLoader {
    static let shared = PerformantImageLoader()
    
    // MARK: - 配置
    private struct LoaderConfig {
        static let maxConcurrentOperations = 6
        static let thumbnailSize = CGSize(width: 300, height: 300)
        static let previewSize = CGSize(width: 800, height: 1200)
        static let fullSize = PHImageManagerMaximumSize
    }
    
    // MARK: - 队列管理
    private let thumbnailQueue: OperationQueue
    private let previewQueue: OperationQueue
    private let fullSizeQueue: OperationQueue
    
    // MARK: - 请求管理
    private var activeRequests: [String: PHImageRequestID] = [:]
    private let requestsLock = NSLock()
    
    private init() {
        // 初始化不同优先级的队列
        thumbnailQueue = OperationQueue()
        thumbnailQueue.name = "com.photoswipe.thumbnail"
        thumbnailQueue.maxConcurrentOperationCount = LoaderConfig.maxConcurrentOperations
        thumbnailQueue.qualityOfService = .userInitiated

        previewQueue = OperationQueue()
        previewQueue.name = "com.photoswipe.preview"
        previewQueue.maxConcurrentOperationCount = 4
        previewQueue.qualityOfService = .userInitiated

        fullSizeQueue = OperationQueue()
        fullSizeQueue.name = "com.photoswipe.fullsize"
        fullSizeQueue.maxConcurrentOperationCount = 2
        fullSizeQueue.qualityOfService = .utility

        // 监听应用生命周期事件
        setupAppLifecycleObservers()
    }

    /// 设置应用生命周期监听
    private func setupAppLifecycleObservers() {
        NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleAppDidEnterBackground()
        }

        NotificationCenter.default.addObserver(
            forName: UIApplication.willEnterForegroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleAppWillEnterForeground()
        }
    }

    /// 应用进入后台时的处理
    private func handleAppDidEnterBackground() {
        print("🔄 PerformantImageLoader: 应用进入后台，暂停队列")
        thumbnailQueue.isSuspended = true
        previewQueue.isSuspended = true
        fullSizeQueue.isSuspended = true
    }

    /// 应用恢复前台时的处理
    private func handleAppWillEnterForeground() {
        print("🔄 PerformantImageLoader: 应用恢复前台，恢复队列")
        thumbnailQueue.isSuspended = false
        previewQueue.isSuspended = false
        fullSizeQueue.isSuspended = false
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - 图片加载方法
    
    /// 加载缩略图（最高优先级）
    func loadThumbnail(for asset: PHAsset, completion: @escaping (UIImage?) -> Void) -> String {
        let requestKey = generateRequestKey(asset: asset, type: .thumbnail)
        
        // 检查缓存
        if let cachedImage = ImageCacheManager.shared.getCachedImage(for: asset.localIdentifier, type: .thumbnail) {
            DispatchQueue.main.async {
                completion(cachedImage)
            }
            return requestKey
        }
        
        let operation = ImageLoadOperation(
            asset: asset,
            targetSize: LoaderConfig.thumbnailSize,
            imageType: .thumbnail,
            requestKey: requestKey
        ) { [weak self] image in
            if let image = image {
                ImageCacheManager.shared.cacheImage(image, for: asset.localIdentifier, type: .thumbnail)
            }
            DispatchQueue.main.async {
                completion(image)
            }
            self?.removeActiveRequest(requestKey)
        }
        
        thumbnailQueue.addOperation(operation)
        return requestKey
    }
    
    /// 加载预览图（中等优先级）
    func loadPreview(for asset: PHAsset, completion: @escaping (UIImage?) -> Void) -> String {
        let requestKey = generateRequestKey(asset: asset, type: .preview)
        
        // 检查缓存
        if let cachedImage = ImageCacheManager.shared.getCachedImage(for: asset.localIdentifier, type: .preview) {
            DispatchQueue.main.async {
                completion(cachedImage)
            }
            return requestKey
        }
        
        let operation = ImageLoadOperation(
            asset: asset,
            targetSize: LoaderConfig.previewSize,
            imageType: .preview,
            requestKey: requestKey
        ) { [weak self] image in
            if let image = image {
                ImageCacheManager.shared.cacheImage(image, for: asset.localIdentifier, type: .preview)
            }
            DispatchQueue.main.async {
                completion(image)
            }
            self?.removeActiveRequest(requestKey)
        }
        
        previewQueue.addOperation(operation)
        return requestKey
    }
    
    /// 加载全尺寸图片（低优先级）
    func loadFullSize(for asset: PHAsset, completion: @escaping (UIImage?) -> Void) -> String {
        let requestKey = generateRequestKey(asset: asset, type: .fullSize)
        
        // 检查缓存
        if let cachedImage = ImageCacheManager.shared.getCachedImage(for: asset.localIdentifier, type: .fullSize) {
            DispatchQueue.main.async {
                completion(cachedImage)
            }
            return requestKey
        }
        
        let operation = ImageLoadOperation(
            asset: asset,
            targetSize: LoaderConfig.fullSize,
            imageType: .fullSize,
            requestKey: requestKey
        ) { [weak self] image in
            if let image = image {
                ImageCacheManager.shared.cacheImage(image, for: asset.localIdentifier, type: .fullSize)
            }
            DispatchQueue.main.async {
                completion(image)
            }
            self?.removeActiveRequest(requestKey)
        }
        
        fullSizeQueue.addOperation(operation)
        return requestKey
    }
    
    /// 批量预加载缩略图
    func preloadThumbnails(for assets: [PHAsset], priority: Operation.QueuePriority = .normal) {
        for asset in assets {
            // 跳过已缓存的图片
            if ImageCacheManager.shared.getCachedImage(for: asset.localIdentifier, type: .thumbnail) != nil {
                continue
            }
            
            let operation = ImageLoadOperation(
                asset: asset,
                targetSize: LoaderConfig.thumbnailSize,
                imageType: .thumbnail,
                requestKey: generateRequestKey(asset: asset, type: .thumbnail)
            ) { image in
                if let image = image {
                    ImageCacheManager.shared.cacheImage(image, for: asset.localIdentifier, type: .thumbnail)
                }
            }
            
            operation.queuePriority = priority
            thumbnailQueue.addOperation(operation)
        }
    }
    
    /// 取消加载请求
    func cancelRequest(_ requestKey: String) {
        requestsLock.lock()
        defer { requestsLock.unlock() }
        
        if let requestID = activeRequests[requestKey] {
            PHImageManager.default().cancelImageRequest(requestID)
            activeRequests.removeValue(forKey: requestKey)
        }
        
        // 取消队列中的操作
        [thumbnailQueue, previewQueue, fullSizeQueue].forEach { queue in
            queue.operations.forEach { operation in
                if let imageOp = operation as? ImageLoadOperation,
                   imageOp.requestKey == requestKey {
                    operation.cancel()
                }
            }
        }
    }
    
    /// 取消所有请求
    func cancelAllRequests() {
        requestsLock.lock()
        defer { requestsLock.unlock() }
        
        activeRequests.values.forEach { requestID in
            PHImageManager.default().cancelImageRequest(requestID)
        }
        activeRequests.removeAll()
        
        [thumbnailQueue, previewQueue, fullSizeQueue].forEach { queue in
            queue.cancelAllOperations()
        }
    }
    
    // MARK: - 私有方法
    
    private func generateRequestKey(asset: PHAsset, type: ImageType) -> String {
        return "\(type.rawValue)_\(asset.localIdentifier)_\(UUID().uuidString)"
    }
    
    private func removeActiveRequest(_ requestKey: String) {
        requestsLock.lock()
        defer { requestsLock.unlock() }
        activeRequests.removeValue(forKey: requestKey)
    }
}

// MARK: - 图片加载操作

private class ImageLoadOperation: Operation, @unchecked Sendable {
    let asset: PHAsset
    let targetSize: CGSize
    let imageType: ImageType
    let requestKey: String
    let completion: (UIImage?) -> Void
    
    private var requestID: PHImageRequestID?
    private var _isExecuting = false
    private var _isFinished = false
    
    init(asset: PHAsset, targetSize: CGSize, imageType: ImageType, requestKey: String, completion: @escaping (UIImage?) -> Void) {
        self.asset = asset
        self.targetSize = targetSize
        self.imageType = imageType
        self.requestKey = requestKey
        self.completion = completion
        super.init()
    }
    
    override var isExecuting: Bool {
        return _isExecuting
    }
    
    override var isFinished: Bool {
        return _isFinished
    }
    
    override var isAsynchronous: Bool {
        return true
    }
    
    override func start() {
        guard !isCancelled else {
            finish()
            return
        }
        
        willChangeValue(forKey: "isExecuting")
        _isExecuting = true
        didChangeValue(forKey: "isExecuting")
        
        let options = createImageRequestOptions()
        
        requestID = PHImageManager.default().requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        ) { [weak self] image, info in
            guard let self = self else { return }

            // 检查是否被取消
            if self.isCancelled {
                self.finish()
                return
            }

            // 检查是否是最终结果
            let isDegraded = (info?[PHImageResultIsDegradedKey] as? Bool) ?? false
            if !isDegraded {
                self.completion(image)
                self.finish()
            } else if image != nil {
                // 如果有降级图片，也调用completion，避免无限等待
                self.completion(image)
                self.finish()
            }
        }

        // 添加超时保护，防止请求永远不返回（减少超时时间）
        DispatchQueue.global().asyncAfter(deadline: .now() + 10.0) { [weak self] in
            guard let self = self else { return }
            if !self.isFinished && !self.isCancelled {
                print("⚠️ 图片加载超时(10s)，强制完成: \(self.asset.localIdentifier)")
                // 取消PHImageManager请求
                if let requestID = self.requestID {
                    PHImageManager.default().cancelImageRequest(requestID)
                }
                self.completion(nil)
                self.finish()
            }
        }
    }
    
    override func cancel() {
        super.cancel()
        if let requestID = requestID {
            PHImageManager.default().cancelImageRequest(requestID)
        }
        finish()
    }
    
    private func createImageRequestOptions() -> PHImageRequestOptions {
        let options = PHImageRequestOptions()
        options.isNetworkAccessAllowed = true
        options.deliveryMode = imageType == .thumbnail ? .fastFormat : .highQualityFormat
        options.resizeMode = .fast
        
        // 针对Live Photos优化
        if asset.mediaSubtypes.contains(.photoLive) {
            options.version = .current
        }
        
        return options
    }
    
    private func finish() {
        willChangeValue(forKey: "isExecuting")
        willChangeValue(forKey: "isFinished")
        _isExecuting = false
        _isFinished = true
        didChangeValue(forKey: "isExecuting")
        didChangeValue(forKey: "isFinished")
    }
}
