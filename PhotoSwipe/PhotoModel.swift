//
//  PhotoModel.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation
import Photos
import UIKit

@Observable
class PhotoModel: Identifiable, Hashable {
    let id = UUID()
    let asset: PHAsset
    var image: UIImage?
    var thumbnail: UIImage?
    private weak var photoService: PhotoService?

    // 加载请求管理
    private var thumbnailRequestKey: String?
    private var imageRequestKey: String?
    
    var isMarkedForDeletion: Bool {
        get {
            return HistoryManager.shared.isPhotoMarked(asset.localIdentifier)
        }
        set {
            if newValue {
                HistoryManager.shared.saveMarkedPhoto(asset.localIdentifier)
            } else {
                HistoryManager.shared.removeMarkForDeletion(photoId: asset.localIdentifier)
            }
        }
    }
    
    var isMarkedForKeeping: Bool {
        get {
            return HistoryManager.shared.isPhotoKept(asset.localIdentifier)
        }
        set {
            if newValue {
                HistoryManager.shared.saveKeptPhoto(asset.localIdentifier)
            } else {
                HistoryManager.shared.removeKeptPhoto(asset.localIdentifier)
            }
        }
    }
    
    init(asset: PHAsset, photoService: PhotoService? = nil) {
        self.asset = asset
        self.photoService = photoService
    }
    
    /// 加载缩略图（快速显示）
    func loadThumbnail() async {
        // 取消之前的请求
        if let requestKey = thumbnailRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
        }

        return await withCheckedContinuation { continuation in
            var hasResumed = false

            thumbnailRequestKey = PerformantImageLoader.shared.loadThumbnail(for: asset) { [weak self] image in
                Task { @MainActor in
                    self?.thumbnail = image
                    self?.thumbnailRequestKey = nil

                    // 确保只resume一次
                    if !hasResumed {
                        hasResumed = true
                        continuation.resume()
                    }
                }
            }

            // 如果thumbnailRequestKey为空（请求失败），立即resume
            if thumbnailRequestKey == nil && !hasResumed {
                hasResumed = true
                continuation.resume()
            }

            // 添加超时保护（减少超时时间）
            DispatchQueue.global().asyncAfter(deadline: .now() + 8.0) {
                if !hasResumed {
                    hasResumed = true
                    print("⚠️ 缩略图加载超时(8s): \(self.asset.localIdentifier)")
                    continuation.resume()
                }
            }
        }
    }
    
    /// 加载高质量图片（用于显示）
    func loadImage() async {
        // 取消之前的请求
        if let requestKey = imageRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
        }

        return await withCheckedContinuation { continuation in
            var hasResumed = false

            imageRequestKey = PerformantImageLoader.shared.loadPreview(for: asset) { [weak self] image in
                Task { @MainActor in
                    self?.image = image
                    self?.imageRequestKey = nil

                    // 确保只resume一次
                    if !hasResumed {
                        hasResumed = true
                        continuation.resume()
                    }
                }
            }

            // 如果imageRequestKey为空（请求失败），立即resume
            if imageRequestKey == nil && !hasResumed {
                hasResumed = true
                continuation.resume()
            }

            // 添加超时保护（减少超时时间）
            DispatchQueue.global().asyncAfter(deadline: .now() + 12.0) {
                if !hasResumed {
                    hasResumed = true
                    print("⚠️ 高质量图片加载超时(12s): \(self.asset.localIdentifier)")
                    continuation.resume()
                }
            }
        }
    }

    /// 取消所有加载请求
    func cancelAllRequests() {
        if let requestKey = thumbnailRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
            thumbnailRequestKey = nil
        }

        if let requestKey = imageRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
            imageRequestKey = nil
        }
    }

    /// 取消所有加载请求（别名，保持兼容性）
    func cancelLoadingRequests() {
        cancelAllRequests()
    }

    /// 重置加载状态并重新加载（用于应用生命周期恢复）
    func resetAndReload() async {
        print("🔄 重置并重新加载照片: \(asset.localIdentifier)")

        // 取消所有进行中的请求
        cancelAllRequests()

        // 清除当前图片状态
        await MainActor.run {
            self.thumbnail = nil
            self.image = nil
        }

        // 重新加载
        await loadThumbnail()
        await loadImage()
    }

    /// 检查是否处于加载状态但没有图片
    var isStuckInLoadingState: Bool {
        return (thumbnailRequestKey != nil && thumbnail == nil) ||
               (imageRequestKey != nil && image == nil)
    }
    
    /// 获取显示用的图片（优先使用高质量，回退到缩略图）
    var displayImage: UIImage? {
        return image ?? thumbnail
    }
    
    // MARK: - Hashable
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: PhotoModel, rhs: PhotoModel) -> Bool {
        return lhs.id == rhs.id
    }
}