//
//  PhotoModel.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation
import Photos
import UIKit

@Observable
class PhotoModel: Identifiable, Hashable {
    let id = UUID()
    let asset: PHAsset
    var image: UIImage?
    var thumbnail: UIImage?
    private weak var photoService: PhotoService?

    // 加载请求管理
    private var thumbnailRequestKey: String?
    private var imageRequestKey: String?
    
    var isMarkedForDeletion: Bool {
        get {
            return HistoryManager.shared.isPhotoMarked(asset.localIdentifier)
        }
        set {
            if newValue {
                HistoryManager.shared.saveMarkedPhoto(asset.localIdentifier)
            } else {
                HistoryManager.shared.removeMarkForDeletion(photoId: asset.localIdentifier)
            }
        }
    }
    
    var isMarkedForKeeping: Bool {
        get {
            return HistoryManager.shared.isPhotoKept(asset.localIdentifier)
        }
        set {
            if newValue {
                HistoryManager.shared.saveKeptPhoto(asset.localIdentifier)
            } else {
                HistoryManager.shared.removeKeptPhoto(asset.localIdentifier)
            }
        }
    }
    
    init(asset: PHAsset, photoService: PhotoService? = nil) {
        self.asset = asset
        self.photoService = photoService
    }
    
    /// 加载缩略图（快速显示）
    func loadThumbnail() async {
        // 取消之前的请求
        if let requestKey = thumbnailRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
        }

        return await withCheckedContinuation { continuation in
            thumbnailRequestKey = PerformantImageLoader.shared.loadThumbnail(for: asset) { [weak self] image in
                Task { @MainActor in
                    self?.thumbnail = image
                    self?.thumbnailRequestKey = nil
                    continuation.resume()
                }
            }
        }
    }
    
    /// 加载高质量图片（用于显示）
    func loadImage() async {
        // 取消之前的请求
        if let requestKey = imageRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
        }

        return await withCheckedContinuation { continuation in
            imageRequestKey = PerformantImageLoader.shared.loadPreview(for: asset) { [weak self] image in
                Task { @MainActor in
                    self?.image = image
                    self?.imageRequestKey = nil
                    continuation.resume()
                }
            }
        }
    }

    /// 取消所有加载请求
    func cancelLoadingRequests() {
        if let requestKey = thumbnailRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
            thumbnailRequestKey = nil
        }

        if let requestKey = imageRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
            imageRequestKey = nil
        }
    }
    
    /// 获取显示用的图片（优先使用高质量，回退到缩略图）
    var displayImage: UIImage? {
        return image ?? thumbnail
    }
    
    // MARK: - Hashable
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: PhotoModel, rhs: PhotoModel) -> Bool {
        return lhs.id == rhs.id
    }
}