//
//  PhotoModel.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation
import Photos
import UIKit

@Observable
class PhotoModel: Identifiable, Hashable {
    let id = UUID()
    let asset: PHAsset
    var image: UIImage?
    var thumbnail: UIImage?
    private weak var photoService: PhotoService?

    // 加载请求管理
    private var thumbnailRequestKey: String?
    private var imageRequestKey: String?
    
    var isMarkedForDeletion: Bool {
        get {
            return HistoryManager.shared.isPhotoMarked(asset.localIdentifier)
        }
        set {
            if newValue {
                HistoryManager.shared.saveMarkedPhoto(asset.localIdentifier)
            } else {
                HistoryManager.shared.removeMarkForDeletion(photoId: asset.localIdentifier)
            }
        }
    }
    
    var isMarkedForKeeping: Bool {
        get {
            return HistoryManager.shared.isPhotoKept(asset.localIdentifier)
        }
        set {
            if newValue {
                HistoryManager.shared.saveKeptPhoto(asset.localIdentifier)
            } else {
                HistoryManager.shared.removeKeptPhoto(asset.localIdentifier)
            }
        }
    }
    
    init(asset: PHAsset, photoService: PhotoService? = nil) {
        self.asset = asset
        self.photoService = photoService
    }
    
    /// 加载缩略图（快速显示）
    func loadThumbnail() async {
        let identifier = asset.localIdentifier
        DebugLogger.shared.log("🖼️ 开始加载缩略图: \(identifier)", category: "PHOTO_LOAD")

        // 取消之前的请求
        if let requestKey = thumbnailRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
            DebugLogger.shared.log("❌ 取消之前的缩略图请求: \(identifier)", category: "PHOTO_LOAD")
        }

        return await withCheckedContinuation { continuation in
            var hasResumed = false
            let resumeLock = NSLock()

            func safeResume() {
                resumeLock.lock()
                defer { resumeLock.unlock() }
                if !hasResumed {
                    hasResumed = true
                    continuation.resume()
                }
            }

            thumbnailRequestKey = PerformantImageLoader.shared.loadThumbnail(for: asset) { [weak self] image in
                Task { @MainActor in
                    self?.thumbnail = image
                    self?.thumbnailRequestKey = nil

                    DebugLogger.shared.log("✅ 缩略图加载完成: \(identifier), 成功: \(image != nil)", category: "PHOTO_LOAD")
                    safeResume()
                }
            }

            // 如果thumbnailRequestKey为空（请求失败），立即resume
            if thumbnailRequestKey == nil {
                DebugLogger.shared.log("❌ 缩略图请求创建失败: \(identifier)", category: "PHOTO_LOAD")
                safeResume()
            }

            // 添加超时保护（减少超时时间）
            DispatchQueue.global().asyncAfter(deadline: .now() + 8.0) {
                DebugLogger.shared.log("⏰ 缩略图加载超时(8s): \(identifier)", category: "PHOTO_LOAD")
                safeResume()
            }
        }
    }
    
    /// 加载高质量图片（用于显示）
    func loadImage() async {
        let identifier = asset.localIdentifier
        DebugLogger.shared.log("🎨 开始加载高质量图片: \(identifier)", category: "PHOTO_LOAD")

        // 取消之前的请求
        if let requestKey = imageRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
            DebugLogger.shared.log("❌ 取消之前的图片请求: \(identifier)", category: "PHOTO_LOAD")
        }

        return await withCheckedContinuation { continuation in
            var hasResumed = false
            let resumeLock = NSLock()

            func safeResume() {
                resumeLock.lock()
                defer { resumeLock.unlock() }
                if !hasResumed {
                    hasResumed = true
                    continuation.resume()
                }
            }

            imageRequestKey = PerformantImageLoader.shared.loadPreview(for: asset) { [weak self] image in
                Task { @MainActor in
                    self?.image = image
                    self?.imageRequestKey = nil

                    DebugLogger.shared.log("✅ 高质量图片加载完成: \(identifier), 成功: \(image != nil)", category: "PHOTO_LOAD")
                    safeResume()
                }
            }

            // 如果imageRequestKey为空（请求失败），立即resume
            if imageRequestKey == nil {
                DebugLogger.shared.log("❌ 高质量图片请求创建失败: \(identifier)", category: "PHOTO_LOAD")
                safeResume()
            }

            // 添加超时保护（减少超时时间）
            DispatchQueue.global().asyncAfter(deadline: .now() + 12.0) {
                DebugLogger.shared.log("⏰ 高质量图片加载超时(12s): \(identifier)", category: "PHOTO_LOAD")
                safeResume()
            }
        }
    }

    /// 取消所有加载请求
    func cancelAllRequests() {
        if let requestKey = thumbnailRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
            thumbnailRequestKey = nil
        }

        if let requestKey = imageRequestKey {
            PerformantImageLoader.shared.cancelRequest(requestKey)
            imageRequestKey = nil
        }
    }

    /// 取消所有加载请求（别名，保持兼容性）
    func cancelLoadingRequests() {
        cancelAllRequests()
    }

    /// 重置加载状态并重新加载（用于应用生命周期恢复）
    func resetAndReload() async {
        let identifier = asset.localIdentifier
        DebugLogger.shared.log("🔄 开始重置并重新加载照片: \(identifier)", category: "LIFECYCLE")

        // 记录当前状态
        await MainActor.run {
            DebugLogger.shared.log("📊 重置前状态 - thumbnail: \(thumbnail != nil), image: \(image != nil), thumbnailKey: \(thumbnailRequestKey != nil), imageKey: \(imageRequestKey != nil)", category: "LIFECYCLE")
        }

        // 取消所有进行中的请求
        cancelAllRequests()
        DebugLogger.shared.log("❌ 已取消所有请求: \(identifier)", category: "LIFECYCLE")

        // 清除当前图片状态
        await MainActor.run {
            self.thumbnail = nil
            self.image = nil
        }
        DebugLogger.shared.log("🧹 已清除图片状态: \(identifier)", category: "LIFECYCLE")

        // 添加短暂延迟确保状态完全清理
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒

        // 重新加载
        DebugLogger.shared.log("🚀 开始重新加载缩略图: \(identifier)", category: "LIFECYCLE")
        await loadThumbnail()

        DebugLogger.shared.log("🚀 开始重新加载高质量图片: \(identifier)", category: "LIFECYCLE")
        await loadImage()

        await MainActor.run {
            DebugLogger.shared.log("✅ 重置完成 - thumbnail: \(thumbnail != nil), image: \(image != nil)", category: "LIFECYCLE")
        }
    }

    /// 检查是否处于加载状态但没有图片
    var isStuckInLoadingState: Bool {
        let stuck = (thumbnailRequestKey != nil && thumbnail == nil) ||
                   (imageRequestKey != nil && image == nil)
        if stuck {
            DebugLogger.shared.log("🚨 检测到卡住状态 - thumbnailKey: \(thumbnailRequestKey != nil), thumbnail: \(thumbnail != nil), imageKey: \(imageRequestKey != nil), image: \(image != nil)", category: "PHOTO_STATE")
        }
        return stuck
    }

    /// 验证照片状态的完整性
    func validateState() -> String {
        let thumbnailStatus = thumbnail != nil ? "✅" : "❌"
        let imageStatus = image != nil ? "✅" : "❌"
        let displayStatus = displayImage != nil ? "✅" : "❌"
        let thumbnailKeyStatus = thumbnailRequestKey != nil ? "🔄" : "⭕"
        let imageKeyStatus = imageRequestKey != nil ? "🔄" : "⭕"

        return "T:\(thumbnailStatus)\(thumbnailKeyStatus) I:\(imageStatus)\(imageKeyStatus) D:\(displayStatus)"
    }
    
    /// 获取显示用的图片（优先使用高质量，回退到缩略图）
    var displayImage: UIImage? {
        return image ?? thumbnail
    }
    
    // MARK: - Hashable
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: PhotoModel, rhs: PhotoModel) -> Bool {
        return lhs.id == rhs.id
    }
}