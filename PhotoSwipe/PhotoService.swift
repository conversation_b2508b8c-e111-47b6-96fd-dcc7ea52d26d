//
//  PhotoService.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation
import Photos
import UIKit

@Observable
class PhotoService {
    var photos: [PhotoModel] = []
    var authorizationStatus: PHAuthorizationStatus = .notDetermined
    var isLoading: Bool = false
    var errorMessage: String?
    
    // 分页加载相关
    private var allAssets: PHFetchResult<PHAsset>?
    private var loadedCount: Int = 0
    private let batchSize: Int = 50 // 每批加载50张
    private var isLoadingMore: Bool = false
    
    // 图片缓存
    private var imageCache: NSCache<NSString, UIImage> = {
        let cache = NSCache<NSString, UIImage>()
        cache.countLimit = 100 // 最多缓存100张图片
        cache.totalCostLimit = 100 * 1024 * 1024 // 100MB
        return cache
    }()
    
    init() {
        authorizationStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
    }
    
    @MainActor
    func requestPhotoLibraryAccess() async {
        let status = await PHPhotoLibrary.requestAuthorization(for: .readWrite)
        authorizationStatus = status
        
        if status == .authorized || status == .limited {
            await loadPhotos()
        } else {
            errorMessage = "需要访问相册权限才能使用此功能"
        }
    }
    
    @MainActor
    private func loadPhotos() async {
        isLoading = true
        errorMessage = nil
        
        let fetchOptions = PHFetchOptions()
        fetchOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        fetchOptions.predicate = NSPredicate(format: "mediaType == %d", PHAssetMediaType.image.rawValue)
        
        allAssets = PHAsset.fetchAssets(with: fetchOptions)
        
        // 重置状态
        photos = []
        loadedCount = 0
        
        // 加载第一批照片
        await loadMorePhotos()
        
        isLoading = false
        
        // 预加载前几张图片的缩略图
        await loadInitialThumbnails()
    }
    
    /// 加载更多照片（分页）
    @MainActor
    func loadMorePhotos() async {
        guard let allAssets = allAssets,
              !isLoadingMore,
              loadedCount < allAssets.count else { return }
        
        isLoadingMore = true
        
        let endIndex = min(loadedCount + batchSize, allAssets.count)
        var newPhotos: [PhotoModel] = []
        
        for i in loadedCount..<endIndex {
            let asset = allAssets.object(at: i)
            let photoModel = PhotoModel(asset: asset, photoService: self)
            newPhotos.append(photoModel)
        }
        
        photos.append(contentsOf: newPhotos)
        loadedCount = endIndex
        isLoadingMore = false
    }
    
    /// 预加载缩略图
    private func loadInitialThumbnails() async {
        let thumbnailsToLoad = min(10, photos.count)
        await withTaskGroup(of: Void.self) { group in
            for i in 0..<thumbnailsToLoad {
                group.addTask {
                    await self.photos[i].loadThumbnail()
                }
            }
        }
    }
    
    /// 获取缓存的图片
    func getCachedImage(for identifier: String) -> UIImage? {
        return imageCache.object(forKey: NSString(string: identifier))
    }
    
    /// 缓存图片
    func cacheImage(_ image: UIImage, for identifier: String) {
        imageCache.setObject(image, forKey: NSString(string: identifier))
    }
    
    /// 检查是否需要加载更多照片
    func checkAndLoadMoreIfNeeded(currentIndex: Int) {
        // 当接近已加载照片的末尾时，自动加载更多
        if currentIndex >= photos.count - 10 {
            Task {
                await loadMorePhotos()
            }
        }
    }
    
    @MainActor
    func deleteMarkedPhotos() async {
        let photosToDelete = photos.filter { $0.isMarkedForDeletion }
        let assetsToDelete = photosToDelete.map { $0.asset }
        let identifiersToDelete = photosToDelete.map { $0.asset.localIdentifier }
        
        guard !assetsToDelete.isEmpty else { return }
        
        do {
            try await PHPhotoLibrary.shared().performChanges {
                PHAssetChangeRequest.deleteAssets(assetsToDelete as NSArray)
            }
            
            // 保存删除历史记录
            HistoryManager.shared.saveDeletedPhotos(identifiersToDelete)
            
            // 从本地数组中移除已删除的照片
            photos.removeAll { $0.isMarkedForDeletion }
            
        } catch {
            errorMessage = "删除照片失败: \(error.localizedDescription)"
        }
    }
    
    /// 删除指定的照片列表
    func deleteSpecificPhotos(_ photosToDelete: [PhotoModel]) async throws {
        guard !photosToDelete.isEmpty else { return }
        
        let assetsToDelete = photosToDelete.map { $0.asset }
        
        try await PHPhotoLibrary.shared().performChanges {
            PHAssetChangeRequest.deleteAssets(assetsToDelete as NSArray)
        }
        
        // 从缓存中移除已删除的照片
        for photo in photosToDelete {
            imageCache.removeObject(forKey: NSString(string: photo.asset.localIdentifier))
        }
        
        // 从本地数组中移除已删除的照片
        let deletedIdentifiers = Set(photosToDelete.map { $0.asset.localIdentifier })
        photos.removeAll { deletedIdentifiers.contains($0.asset.localIdentifier) }
    }
    
    /// 清理缓存
    func clearCache() {
        imageCache.removeAllObjects()
    }
}