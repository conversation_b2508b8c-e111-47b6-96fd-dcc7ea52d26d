<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22758" systemVersion="23F79" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="PhotoRecord" representedClassName="PhotoRecord" syncable="YES" codeGenerationType="class">
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="isDeleted" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isKept" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isMarkedForDeletion" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="lastModified" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="photoIdentifier" optional="NO" attributeType="String"/>
        <attribute name="processingOrder" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
    </entity>
    <entity name="UserSession" representedClassName="UserSession" syncable="YES" codeGenerationType="class">
        <attribute name="currentPhotoIndex" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="lastActiveDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="sessionId" optional="NO" attributeType="String"/>
        <attribute name="totalPhotosCount" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
    </entity>
</model>
