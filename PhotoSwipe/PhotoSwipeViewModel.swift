//
//  PhotoSwipeViewModel.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation
import Photos
import SwiftUI

@Observable
class PhotoSwipeViewModel {
    let photoService = PhotoService()
    var currentPhotoIndex: Int = 0
    var showingDeleteConfirmation: Bool = false
    var showingPermissionAlert: Bool = false
    var isDeleting: Bool = false
    
    var currentPhoto: PhotoModel? {
        guard currentPhotoIndex < photoService.photos.count else { return nil }
        
        // 检查是否需要加载更多照片
        photoService.checkAndLoadMoreIfNeeded(currentIndex: currentPhotoIndex)
        
        return photoService.photos[currentPhotoIndex]
    }
    
    var hasNextPhoto: Bool {
        return currentPhotoIndex < photoService.photos.count - 1
    }
    
    var hasPreviousPhoto: Bool {
        return currentPhotoIndex > 0
    }
    
    /// 获取标记照片数量
    var markedPhotosCount: Int {
        let count = CoreDataManager.shared.getMarkedPhotos().count
        print("📊 当前标记删除照片数量: \(count)")
        return count
    }

    /// 获取保留照片数量
    var keptPhotosCount: Int {
        let count = CoreDataManager.shared.getKeptPhotos().count
        print("📊 当前保留照片数量: \(count)")
        return count
    }
    
    /// 获取已处理照片数量（标记删除 + 保留）
    var processedPhotosCount: Int {
        return photoService.photos.filter { $0.isMarkedForDeletion || $0.isMarkedForKeeping }.count
    }
    
    @MainActor
    func requestPermissionAndLoadPhotos() async {
        if photoService.authorizationStatus == .notDetermined {
            await photoService.requestPhotoLibraryAccess()
        } else if photoService.authorizationStatus == .denied || photoService.authorizationStatus == .restricted {
            showingPermissionAlert = true
        } else {
            await photoService.requestPhotoLibraryAccess()
        }
        
        // 照片加载完成后，恢复上次保存的位置
        restoreSavedPosition()
    }
    
    /// 移动到第一张未标记删除的照片
    private func moveToFirstUnmarkedPhoto() {
        for (index, photo) in photoService.photos.enumerated() {
            if !photo.isMarkedForDeletion {
                currentPhotoIndex = index
                return
            }
        }
        // 如果所有照片都被标记了，保持在第一张
        currentPhotoIndex = 0
    }
    
    /// 恢复上次保存的位置
    private func restoreSavedPosition() {
        let savedPosition = HistoryManager.shared.getSavedPosition()
        
        // 如果有保存的位置且索引在有效范围内，恢复位置
        if savedPosition.index > 0 && savedPosition.index < photoService.photos.count {
            currentPhotoIndex = savedPosition.index
        } else {
            // 否则移动到第一张未处理的照片
            moveToFirstUnprocessedPhoto()
        }
    }
    
    /// 移动到第一张未处理的照片（既未删除也未保留）
    private func moveToFirstUnprocessedPhoto() {
        for (index, photo) in photoService.photos.enumerated() {
            if !photo.isMarkedForDeletion && !photo.isMarkedForKeeping {
                currentPhotoIndex = index
                return
            }
        }
        // 如果所有照片都被处理了，保持在第一张
        currentPhotoIndex = 0
    }
    
    /// 保存当前位置
    func saveCurrentPosition() {
        HistoryManager.shared.saveCurrentPosition(currentPhotoIndex, totalCount: photoService.photos.count)
    }
    
    func swipeLeft() {
        // 左滑表示"保留"，标记为保留
        if let currentPhoto = currentPhoto {
            currentPhoto.isMarkedForKeeping = true
            // 保存到UserDefaults
            CoreDataManager.shared.savePhotoDecision(
                photoIdentifier: currentPhoto.asset.localIdentifier,
                decision: .keep
            )
            print("📱 左滑保留照片: \(currentPhoto.asset.localIdentifier)")
        }
        moveToNextPhoto()
    }

    func swipeRight() {
        // 右滑表示"删除"，标记为待删除
        if let currentPhoto = currentPhoto {
            currentPhoto.isMarkedForDeletion = true
            // 保存到UserDefaults
            CoreDataManager.shared.savePhotoDecision(
                photoIdentifier: currentPhoto.asset.localIdentifier,
                decision: .markForDeletion
            )
            print("📱 右滑标记删除照片: \(currentPhoto.asset.localIdentifier)")
        }
        moveToNextPhoto()
    }
    
    private func moveToNextPhoto() {
        if hasNextPhoto {
            // 直接移动到下一张照片，不跳过任何照片
            currentPhotoIndex += 1
            
            // 预加载下一张图片
            Task {
                if currentPhotoIndex + 1 < photoService.photos.count {
                    await photoService.photos[currentPhotoIndex + 1].loadImage()
                }
            }
            
            // 保存当前位置
            saveCurrentPosition()
            
            // 预加载附近照片
            preloadNearbyPhotos()
        }
    }
    
    func moveToPreviousPhoto() {
        if hasPreviousPhoto {
            // 直接移动到上一张照片，不跳过任何照片
            currentPhotoIndex -= 1
            
            // 保存当前位置
            saveCurrentPosition()
            
            // 预加载附近照片
            preloadNearbyPhotos()
        }
    }
    
    /// 预加载当前照片附近的照片（优化版本）
    private func preloadNearbyPhotos() {
        Task {
            let preloadRange = 3 // 增加预加载范围
            let startIndex = max(0, currentPhotoIndex - preloadRange)
            let endIndex = min(photoService.photos.count - 1, currentPhotoIndex + preloadRange)

            // 收集需要预加载的照片
            var assetsToPreload: [PHAsset] = []
            var photosToLoadImage: [PhotoModel] = []

            for i in startIndex...endIndex {
                let photo = photoService.photos[i]

                // 预加载缩略图
                if photo.thumbnail == nil {
                    assetsToPreload.append(photo.asset)
                }

                // 对于当前照片前后1张，预加载高质量图片
                if abs(i - currentPhotoIndex) <= 1 && photo.image == nil {
                    photosToLoadImage.append(photo)
                }
            }

            // 批量预加载缩略图
            if !assetsToPreload.isEmpty {
                PerformantImageLoader.shared.preloadThumbnails(for: assetsToPreload, priority: .normal)
            }

            // 预加载高质量图片
            for photo in photosToLoadImage {
                await photo.loadImage()
            }
        }
    }
    

    
    @MainActor
    func deleteMarkedPhotos() async {
        isDeleting = true
        await photoService.deleteMarkedPhotos()
        // 重置当前索引如果超出范围
        if currentPhotoIndex >= photoService.photos.count {
            currentPhotoIndex = max(0, photoService.photos.count - 1)
        }
        isDeleting = false
    }
    
    @MainActor
    func loadPhotos() async {
        await photoService.requestPhotoLibraryAccess()
        // 重置当前索引如果超出范围
        if currentPhotoIndex >= photoService.photos.count {
            currentPhotoIndex = max(0, photoService.photos.count - 1)
        }
    }
    
    func showDeleteConfirmation() {
        if markedPhotosCount > 0 {
            showingDeleteConfirmation = true
        }
    }
    
    // MARK: - 历史记录相关功能
    
    /// 获取历史记录统计信息
    func getHistoryStats() -> (markedCount: Int, deletedCount: Int) {
        return HistoryManager.shared.getHistoryStats()
    }
    
    /// 获取扩展的历史记录统计信息
    func getExtendedHistoryStats() -> (markedCount: Int, deletedCount: Int, keptCount: Int) {
        return HistoryManager.shared.getExtendedHistoryStats()
    }
    
    /// 清除所有标记
    func clearAllMarks() {
        HistoryManager.shared.clearAllMarks()
    }
    
    /// 清除所有历史记录
    func clearAllHistory() {
        HistoryManager.shared.clearAllHistory()
    }
    
    /// 清除所有记录（包括保留记录和位置）
    func clearAllRecords() {
        HistoryManager.shared.clearAllRecords()
    }
    
    /// 检查是否有历史记录
    func hasHistory() -> Bool {
        let stats = getExtendedHistoryStats()
        return stats.markedCount > 0 || stats.deletedCount > 0 || stats.keptCount > 0
    }
    
    /// 获取保留的照片列表
    func getKeptPhotos() -> [PhotoModel] {
        let keptIdentifiers = CoreDataManager.shared.getKeptPhotos()
        return photoService.photos.filter { photo in
            keptIdentifiers.contains(photo.asset.localIdentifier)
        }
    }
    
    /// 重置照片的保留标记
    func resetPhotoKeepMark(_ photo: PhotoModel) {
        photo.isMarkedForKeeping = false
    }
    
    /// 删除指定的照片列表
    @MainActor
    func deleteSpecificPhotos(_ photosToDelete: [PhotoModel]) async {
        guard !photosToDelete.isEmpty else { return }
        
        isDeleting = true
        
        do {
            // 保存到历史记录
            for photo in photosToDelete {
                HistoryManager.shared.saveDeletedPhoto(photoId: photo.asset.localIdentifier)
            }
            
            // 执行删除
            try await photoService.deleteSpecificPhotos(photosToDelete)
            
            // 重新加载照片
            await loadPhotos()
            
        } catch {
            print("删除照片失败: \(error)")
        }
        
        isDeleting = false
    }

    /// 处理内存警告
    func handleMemoryWarning() {
        photoService.handleMemoryWarning()

        // 清理当前不需要的图片引用
        let currentIndex = currentPhotoIndex
        for (index, photo) in photoService.photos.enumerated() {
            // 保留当前照片和前后各2张照片的缩略图，清除其他
            if abs(index - currentIndex) > 2 {
                photo.image = nil
                if abs(index - currentIndex) > 5 {
                    photo.thumbnail = nil
                }
            }
        }
    }

    /// 智能预加载管理
    func optimizePreloading() {
        // 取消远离当前位置的加载请求
        let currentIndex = currentPhotoIndex
        for (index, photo) in photoService.photos.enumerated() {
            if abs(index - currentIndex) > 5 {
                photo.cancelLoadingRequests()
            }
        }

        // 触发附近照片的预加载
        preloadNearbyPhotos()
    }
}