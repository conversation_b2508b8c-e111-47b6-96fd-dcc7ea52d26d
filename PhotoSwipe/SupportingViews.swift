//
//  SupportingViews.swift
//  PhotoSwipe
//
//  Created by <PERSON> on 4/6/25.
//

import SwiftUI

struct PermissionDeniedView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "photo.on.rectangle.angled")
                .font(.system(size: 80))
                .foregroundColor(.gray)
            
            Text("需要访问相册")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("请在设置中允许访问照片，以便浏览和管理您的图片。")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal)
            
            Button("打开设置") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
    }
}

struct LoadingView: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("正在加载照片...")
                .font(.title3)
                .foregroundColor(.secondary)
        }
    }
}

struct EmptyPhotosView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "photo.stack")
                .font(.system(size: 80))
                .foregroundColor(.gray)
            
            Text("没有找到照片")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("您的相册中没有照片，请先添加一些照片。")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal)
        }
        .padding()
    }
}

#Preview {
    VStack(spacing: 50) {
        PermissionDeniedView()
        LoadingView()
        EmptyPhotosView()
    }
}